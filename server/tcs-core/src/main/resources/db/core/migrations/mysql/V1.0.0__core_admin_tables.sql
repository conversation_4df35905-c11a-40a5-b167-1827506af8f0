CREATE TABLE IF NOT EXISTS diskfile (
    fileid BIGINT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    filepath VARCHAR(128) NOT NULL,
    filename VARCHAR(128) NOT NULL,
    status INT,
    createtime TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS resourcestructure (
    resourcestructureid INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    sceneid INT,
    structuretypeid INT,
    resourcestructurename VARCHAR(128),
    parentresourcestructureid INT,
    photo VARCHAR(256),
    `position` VARCHAR(256),
    levelofpath VARCHAR(128),
    display INT,
    sortvalue INT,
    extendedfield TEXT,
    originid INT,
    originparentid INT
);

-- CREATE INDEX idx_resourcestructure_levelofpath ON resourcestructure (levelofpath);

CREATE TABLE IF NOT EXISTS rolepermissionmap (
    rolepermissionmapid INT AUTO_INCREMENT NOT NULL PRIMARY KEY,
    roleid INT,
    permissioncategoryid INT,
    permissionid INT
);

-- CREATE INDEX idx_permissioncategoryid ON rolepermissionmap (permissioncategoryid);

-- CREATE UNIQUE INDEX idx_systemconfigkey ON systemconfig (systemconfigkey);