CREATE TABLE complexindex (
    complexindexid INT GENERATED BY DEFAULT AS IDENTITY NOT NULL PRIMARY KEY,
    complexindexname VARCHAR(128),
    complexindexdefinitionid INT,
    objectid INT,
    calccron VARCHAR(128),
    calctype INT,
    aftercalc VARCHAR(128),
    savecron VARCHAR(128),
    expression TEXT,
    unit VARCHAR(128),
    accuracy VARCHAR(128),
    objecttypeid INT,
    remark VARCHAR(128),
    label VARCHAR(128),
    businesstypeid INT,
    checkexpression VARCHAR(1024)
);