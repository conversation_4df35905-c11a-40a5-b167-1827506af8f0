-- Account table
CREATE TABLE IF NOT EXISTS tbl_account (
  UserId INT NOT NULL,
  UserName VARCHAR(128) NOT NULL,
  LogonId VARCHAR(20) NOT NULL,
  Password VARCHAR(128) DEFAULT NULL,
  Enable BOOLEAN NOT NULL DEFAULT TRUE,
  MaxError INT DEFAULT NULL,
  Locked BOOLEAN NOT NULL DEFAULT FALSE,
  ValidTime TIMESTAMP DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  IsRemote BOOLEAN NOT NULL DEFAULT FALSE,
  CenterId INT DEFAULT NULL,
  PasswordValidTime TIMESTAMP DEFAULT NULL,
  ThemeName VARCHAR(128) DEFAULT NULL,
  NeedResetPwd TINYINT NOT NULL DEFAULT 0,
  Avatar VARCHAR(256) DEFAULT NULL,
  PRIMARY KEY (UserId)
);

-- Config change tables
CREATE TABLE IF NOT EXISTS tbl_configchangedefine (
  ConfigId INT NOT NULL,
  EntityName VARCHAR(255) NOT NULL,
  TableName VARCHAR(255) NOT NULL,
  IdDefine VARCHAR(255) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS tbl_configchangemacrolog (
  ObjectId VARCHAR(255) NOT NULL,
  ConfigId INT NOT NULL,
  EditType INT NOT NULL,
  UpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);
-- CREATE INDEX IDX_configchangemacrolog_1 ON tbl_configchangemacrolog(UpdateTime DESC, ConfigId);

CREATE TABLE IF NOT EXISTS tbl_configchangemicrolog (
  ObjectId VARCHAR(255) NOT NULL,
  ConfigId INT NOT NULL,
  EditType INT NOT NULL,
  UpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);
-- CREATE INDEX IDX_configchangemicrolog_1 ON tbl_configchangemicrolog(ObjectId, ConfigId, EditType);

CREATE TABLE IF NOT EXISTS tbl_controllogaction (
  LogActionId INT NOT NULL,
  ActionId INT NOT NULL,
  ActionName VARCHAR(50) DEFAULT NULL,
  EquipmentId INT DEFAULT NULL,
  ControlId INT DEFAULT NULL,
  ActionValue VARCHAR(255) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS tbl_controlmeanings (
  Id INT NOT NULL AUTO_INCREMENT,
  EquipmentTemplateId INT NOT NULL,
  ControlId INT NOT NULL,
  ParameterValue SMALLINT NOT NULL,
  Meanings VARCHAR(255) DEFAULT NULL,
  BaseCondId DECIMAL(12,0) DEFAULT NULL,
  PRIMARY KEY (Id)
);
-- CREATE INDEX IDX_TBLControlMeanings_1 ON tbl_controlmeanings(EquipmentTemplateId, ControlId);
-- CREATE INDEX IDX_DataItem_EntryId ON tbl_dataitem(EntryId);
-- CREATE INDEX IDC_EquipmentId_MonitorUnit_ID ON tbl_equipment(MonitorUnitId);
-- CREATE INDEX IDX_EquipmentTemplateId ON tbl_equipment(EquipmentTemplateId);

-- Event tables
CREATE TABLE IF NOT EXISTS tbl_event (
  Id INT NOT NULL AUTO_INCREMENT,
  EquipmentTemplateId INT NOT NULL,
  EventId INT NOT NULL,
  EventName VARCHAR(128) NOT NULL,
  StartType INT NOT NULL,
  EndType INT NOT NULL,
  StartExpression TEXT,
  SuppressExpression TEXT,
  EventCategory INT NOT NULL,
  SignalId INT DEFAULT NULL,
  Enable BOOLEAN NOT NULL,
  Visible BOOLEAN NOT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  DisplayIndex INT DEFAULT NULL,
  ModuleNo INT NOT NULL DEFAULT 0,
  PRIMARY KEY (Id)
);
-- CREATE INDEX IDX_TBLEvent_1 ON tbl_event(EquipmentTemplateId);
-- CREATE INDEX IDX_TBLEventCondition_1 ON tbl_eventcondition(EquipmentTemplateId, EventId);

CREATE TABLE IF NOT EXISTS tbl_eventlogaction (
  LogActionId INT NOT NULL,
  ActionName VARCHAR(255) NOT NULL,
  StationId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  TriggerType INT NOT NULL,
  StartExpression VARCHAR(255) DEFAULT NULL,
  SuppressExpression VARCHAR(255) DEFAULT NULL,
  InformMsg VARCHAR(255) DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL
);

-- Station and structure tables
CREATE TABLE IF NOT EXISTS tbl_house (
  HouseId INT NOT NULL,
  StationId INT NOT NULL,
  HouseName VARCHAR(128) NOT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  LastUpdateDate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (HouseId, StationId)
);

CREATE TABLE IF NOT EXISTS tbl_logicclassentry (
  EntryId INT NOT NULL,
  EntryCategory INT DEFAULT NULL,
  LogicClassId INT DEFAULT NULL,
  LogicClass VARCHAR(128) DEFAULT NULL,
  StandardType INT NOT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (EntryId, StandardType)
);

CREATE TABLE IF NOT EXISTS tbl_loginformlist (
  LogActionId INT NOT NULL,
  InformerId INT NOT NULL,
  UserId INT DEFAULT NULL,
  InfoType INT DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS tbl_monitorunitprojectinfo (
  StationId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  ProjectName VARCHAR(255) DEFAULT NULL,
  ContractNo VARCHAR(255) DEFAULT NULL,
  InstallTime TIMESTAMP DEFAULT NULL,
  CONSTRAINT TBL_MUProjectInfo_IDX1 UNIQUE (StationId, MonitorUnitId)
);

CREATE TABLE IF NOT EXISTS tbl_operationdetail (
  UserId INT NOT NULL,
  ObjectId VARCHAR(128) DEFAULT NULL,
  ObjectType INT NOT NULL,
  PropertyName VARCHAR(128) DEFAULT NULL,
  OperationTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  OperationType VARCHAR(64) NOT NULL,
  OldValue VARCHAR(4000) DEFAULT NULL,
  NewValue VARCHAR(4000) DEFAULT NULL
);

CREATE TABLE IF NOT EXISTS tbl_originbussinesscategorymap (
  EquipmentTemplateId INT NOT NULL,
  OriginCategory INT NOT NULL,
  PRIMARY KEY (EquipmentTemplateId)
);

CREATE TABLE IF NOT EXISTS tbl_primarykeyidentity (
  TableId INT NOT NULL,
  TableName VARCHAR(30) DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (TableId)
);

CREATE TABLE IF NOT EXISTS tbl_primarykeyvalue (
  TableId INT NOT NULL,
  PostalCode INT NOT NULL,
  MinValue INT DEFAULT NULL,
  CurrentValue INT DEFAULT NULL,
  PRIMARY KEY (PostalCode, TableId)
);

-- Signal tables
CREATE TABLE IF NOT EXISTS tbl_signal (
  Id INT NOT NULL AUTO_INCREMENT,
  EquipmentTemplateId INT NOT NULL,
  SignalId INT NOT NULL,
  Enable BOOLEAN NOT NULL,
  Visible BOOLEAN NOT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  SignalName VARCHAR(128) NOT NULL,
  SignalCategory INT NOT NULL,
  SignalType INT NOT NULL,
  ChannelNo INT NOT NULL,
  ChannelType INT NOT NULL,
  Expression TEXT,
  DataType INT DEFAULT NULL,
  ShowPrecision VARCHAR(20) DEFAULT NULL,
  Unit VARCHAR(64) DEFAULT NULL,
  StoreInterval DOUBLE DEFAULT NULL,
  AbsValueThreshold DOUBLE DEFAULT NULL,
  PercentThreshold DOUBLE DEFAULT NULL,
  StaticsPeriod INT DEFAULT NULL,
  BaseTypeId DECIMAL(12,0) DEFAULT NULL,
  ChargeStoreInterVal DOUBLE DEFAULT NULL,
  ChargeAbsValue DOUBLE DEFAULT NULL,
  DisplayIndex INT NOT NULL,
  MDBSignalId INT DEFAULT NULL,
  ModuleNo INT NOT NULL DEFAULT 0,
  PRIMARY KEY (Id)
);
-- CREATE INDEX IDX_TBLSignal_1 ON tbl_signal(EquipmentTemplateId);

CREATE TABLE IF NOT EXISTS tbl_signalmeanings (
  Id INT NOT NULL AUTO_INCREMENT,
  EquipmentTemplateId INT NOT NULL,
  SignalId INT NOT NULL,
  StateValue SMALLINT NOT NULL,
  Meanings VARCHAR(255) DEFAULT NULL,
  BaseCondId DECIMAL(12,0) DEFAULT NULL,
  PRIMARY KEY (Id),
  CONSTRAINT CLUSTERED UNIQUE (EquipmentTemplateId, SignalId, StateValue)
);
-- CREATE INDEX IDX_TBLSignalMeanings_1 ON tbl_signalmeanings(EquipmentTemplateId, SignalId);

CREATE TABLE IF NOT EXISTS tbl_stationbasemap (
  StationBaseType INT NOT NULL,
  StationCategory INT NOT NULL,
  StandardType INT NOT NULL,
  PRIMARY KEY (StandardType, StationBaseType, StationCategory)
);

CREATE TABLE IF NOT EXISTS tbl_stationbasetype (
  Id INT NOT NULL,
  StandardId INT NOT NULL,
  Type VARCHAR(128) DEFAULT NULL,
  PRIMARY KEY (Id, StandardId)
);

CREATE TABLE IF NOT EXISTS tbl_stationmask (
  StationId INT NOT NULL,
  TimeGroupId INT DEFAULT NULL,
  Reason VARCHAR(255) DEFAULT NULL,
  StartTime TIMESTAMP DEFAULT NULL,
  EndTime TIMESTAMP DEFAULT NULL,
  UserId INT DEFAULT NULL,
  PRIMARY KEY (StationId)
);

CREATE TABLE IF NOT EXISTS tbl_stationprojectinfo (
  StationId INT NOT NULL,
  ProjectName VARCHAR(255) DEFAULT NULL,
  ContractNo VARCHAR(255) DEFAULT NULL,
  InstallTime TIMESTAMP DEFAULT NULL,
  CONSTRAINT TBL_StationProjectInfo_IDX1 UNIQUE (StationId)
);

CREATE TABLE IF NOT EXISTS tbl_stationstructure (
  StructureId INT NOT NULL,
  StructureGroupId INT NOT NULL,
  ParentStructureId INT NOT NULL,
  StructureName VARCHAR(128) NOT NULL,
  IsUngroup BOOLEAN NOT NULL,
  StructureType INT NOT NULL,
  MapZoom DOUBLE DEFAULT NULL,
  Longitude DECIMAL(22,17) DEFAULT NULL,
  Latitude DECIMAL(22,17) DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  LevelPath VARCHAR(200) NOT NULL,
  Enable BOOLEAN NOT NULL,
  PRIMARY KEY (StructureId)
);

CREATE TABLE IF NOT EXISTS tbl_stationstructuremap (
  StructureId INT NOT NULL,
  StationId INT NOT NULL,
  PRIMARY KEY (StationId, StructureId)
);

CREATE TABLE IF NOT EXISTS tbl_stationswatchmap (
  SwatchStationId INT NOT NULL,
  StationId INT NOT NULL
);

CREATE TABLE IF NOT EXISTS tbl_swatchstation (
  SwatchStationId INT NOT NULL AUTO_INCREMENT,
  SwatchStationName VARCHAR(128) NOT NULL,
  StationId INT NOT NULL,
  CreateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  Description VARCHAR(255) DEFAULT NULL
);
-- CREATE INDEX SwatchStationId ON tbl_swatchstation(SwatchStationId);

CREATE TABLE IF NOT EXISTS tbl_userrolemap (
  UserId INT NOT NULL,
  RoleId INT NOT NULL,
  PRIMARY KEY (RoleId, UserId)
);

CREATE TABLE IF NOT EXISTS tbl_workstation (
  WorkStationId INT NOT NULL,
  WorkStationName VARCHAR(255) NOT NULL,
  WorkStationType INT NOT NULL,
  IPAddress VARCHAR(64) NOT NULL,
  ParentId INT NOT NULL DEFAULT 0,
  ConnectState INT NOT NULL,
  UpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  IsUsed BOOLEAN NOT NULL DEFAULT TRUE,
  CPU DOUBLE DEFAULT NULL,
  Memory DOUBLE DEFAULT NULL,
  ThreadCount INT DEFAULT NULL,
  DiskFreeSpace DOUBLE DEFAULT NULL,
  DBFreeSpace DOUBLE DEFAULT NULL,
  LastCommTime TIMESTAMP DEFAULT NULL,
  PRIMARY KEY (WorkStationId)
);

CREATE TABLE IF NOT EXISTS tbl_writebackentry (
  EntryId INT NOT NULL,
  EntryCategory INT DEFAULT NULL,
  EntryName VARCHAR(128) DEFAULT NULL,
  EntryTitle VARCHAR(128) DEFAULT NULL,
  EntryAlias VARCHAR(255) DEFAULT NULL,
  Enable BOOLEAN NOT NULL DEFAULT TRUE,
  Description VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (EntryId)
);

-- TSL tables
CREATE TABLE IF NOT EXISTS tsl_acrossmonitorunitsignal (
  StationId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  EquipmentId INT NOT NULL,
  SignalId INT NOT NULL,
  Expression LONGTEXT,
  PRIMARY KEY (EquipmentId, SignalId, StationId)
);

CREATE TABLE IF NOT EXISTS tsl_channelmap (
  SamplerUnitId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  OriginalChannelNo INT NOT NULL,
  StandardChannelNo INT NOT NULL,
  PRIMARY KEY (SamplerUnitId)
);

CREATE TABLE IF NOT EXISTS tsl_monitorunit (
  MonitorUnitId INT NOT NULL,
  MonitorUnitName VARCHAR(128) NOT NULL,
  MonitorUnitCategory INT NOT NULL,
  MonitorUnitCode VARCHAR(128) NOT NULL,
  WorkStationId INT DEFAULT NULL,
  StationId INT DEFAULT NULL,
  IpAddress VARCHAR(128) DEFAULT NULL,
  RunMode INT DEFAULT NULL,
  ConfigFileCode VARCHAR(32) DEFAULT NULL,
  ConfigUpdateTime TIMESTAMP DEFAULT NULL,
  SampleConfigCode VARCHAR(32) DEFAULT NULL,
  SoftwareVersion VARCHAR(64) DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  StartTime TIMESTAMP DEFAULT NULL,
  HeartbeatTime TIMESTAMP DEFAULT NULL,
  ConnectState INT NOT NULL DEFAULT 2,
  UpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  IsSync BOOLEAN NOT NULL DEFAULT TRUE,
  SyncTime TIMESTAMP DEFAULT NULL,
  IsConfigOK BOOLEAN NOT NULL DEFAULT TRUE,
  ConfigFileCode_Old VARCHAR(32) DEFAULT NULL,
  SampleConfigCode_Old VARCHAR(32) DEFAULT NULL,
  AppCongfigId INT DEFAULT NULL,
  CanDistribute BOOLEAN NOT NULL,
  Enable BOOLEAN NOT NULL,
  ProjectName VARCHAR(255) DEFAULT NULL,
  ContractNo VARCHAR(255) DEFAULT NULL,
  InstallTime TIMESTAMP DEFAULT NULL,
  FSU BOOLEAN DEFAULT FALSE,
  PRIMARY KEY (MonitorUnitId)
);
-- CREATE INDEX IDX_MonitorUnit_WorkStationId ON tsl_monitorunit(WorkStationId);

CREATE TABLE IF NOT EXISTS tsl_monitorunitevent (
  StationId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  EquipmentId INT NOT NULL,
  EventId INT NOT NULL,
  StartExpression VARCHAR(255) DEFAULT NULL,
  SuppressExpression TEXT,
  PRIMARY KEY (EquipmentId, EventId, StationId)
);

CREATE TABLE IF NOT EXISTS tsl_monitorunitsignal (
  StationId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  EquipmentId INT NOT NULL,
  SignalId INT NOT NULL,
  ReferenceSamplerUnitId INT DEFAULT NULL,
  ReferenceChannelNo INT DEFAULT NULL,
  Expression TEXT,
  InstanceType INT NOT NULL,
  PRIMARY KEY (EquipmentId, SignalId, StationId)
);

CREATE TABLE IF NOT EXISTS tsl_port (
  Id INT NOT NULL AUTO_INCREMENT,
  PortId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  PortNo INT NOT NULL,
  PortName VARCHAR(128) NOT NULL,
  PortType INT NOT NULL,
  Setting VARCHAR(255) NOT NULL,
  PhoneNumber VARCHAR(128) DEFAULT NULL,
  LinkSamplerUnitId INT DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (Id)
);
-- CREATE INDEX IDX_Port_1 ON tsl_port(MonitorUnitId, PortId);

CREATE TABLE IF NOT EXISTS tsl_samplerunit (
  Id INT NOT NULL AUTO_INCREMENT,
  SamplerUnitId INT NOT NULL,
  PortId INT NOT NULL,
  MonitorUnitId INT NOT NULL,
  SamplerId INT NOT NULL,
  ParentSamplerUnitId INT NOT NULL,
  SamplerType INT NOT NULL,
  SamplerUnitName VARCHAR(128) NOT NULL,
  Address INT NOT NULL,
  SpUnitInterval DOUBLE DEFAULT NULL,
  DllPath VARCHAR(128) DEFAULT NULL,
  ConnectState INT NOT NULL,
  UpdateTime TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  PhoneNumber VARCHAR(128) DEFAULT NULL,
  Description VARCHAR(255) DEFAULT NULL,
  PRIMARY KEY (Id)
);
-- CREATE INDEX IDX_SamplerUnit_1 ON tsl_samplerunit(MonitorUnitId, SamplerUnitId);