package com.siteweb.tcs.tracking.connector.process;

import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.tracking.connector.letter.TrackingStrategyMessage;
import com.siteweb.tcs.tracking.dal.entity.TrackingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.HashMap;
import java.util.Map;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 埋点策略管理Actor
 * 负责管理埋点策略的生命周期
 */
@Slf4j
public class TrackingStrategyManager extends AbstractActor {

    private final ActorProbe probe;
    private final ActorRef trackingDataAdapter;
    
    // 策略缓存
    private final Map<Integer, TrackingStrategy> strategyCache = new HashMap<>();
    
    // 日志队列名称
    private static final String STRATEGY_LOG = "StrategyLog";

    /**
     * 创建TrackingStrategyManager的Props
     */
    public static Props props(ActorRef trackingDataAdapter) {
        return Props.create(TrackingStrategyManager.class, trackingDataAdapter);
    }

    /**
     * 构造函数
     */
    public TrackingStrategyManager(ActorRef trackingDataAdapter) {
        this.trackingDataAdapter = trackingDataAdapter;
        this.probe = createProbe(this);
        
        // 初始化日志队列
        probe.addWindowLog(STRATEGY_LOG);
        
        // 初始化计数器
        probe.addCounter("StrategyCreateCounter");
        probe.addCounter("StrategyUpdateCounter");
        probe.addCounter("StrategyDeleteCounter");
        
        // 初始化仪表盘
        probe.addGauge("ActiveStrategies");
        probe.addGauge("InactiveStrategies");
        probe.addGauge("PausedStrategies");
        
        // 日志记录
        probe.info("TrackingStrategyManager initialized successfully");
    }

    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(TrackingStrategyMessage.class, this::handleTrackingStrategy)
                .matchAny(this::unhandled)
                .build();
    }

    /**
     * 处理埋点策略消息
     */
    private void handleTrackingStrategy(TrackingStrategyMessage message) {
        // 记录日志
        probe.enqueueWindowLogItem(STRATEGY_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Processing strategy message: " + message.getWindowLogString()));
        
        // 根据操作类型处理
        switch (message.getOperationType()) {
            case CREATE:
                createStrategy(message.getStrategy());
                break;
            case UPDATE:
                updateStrategy(message.getStrategy());
                break;
            case DELETE:
                deleteStrategy(message.getStrategyId());
                break;
            case ACTIVATE:
                activateStrategy(message.getStrategyId());
                break;
            case DEACTIVATE:
                deactivateStrategy(message.getStrategyId());
                break;
            case PAUSE:
                pauseStrategy(message.getStrategyId());
                break;
            default:
                probe.warn("Unknown strategy operation: " + message.getOperationType());
                break;
        }
        
        // 更新仪表盘
        updateStrategyGauges();
    }

    /**
     * 创建策略
     */
    private void createStrategy(TrackingStrategy strategy) {
        if (strategy == null || strategy.getId() == null) {
            probe.error("Invalid strategy for creation");
            return;
        }
        
        // 添加到缓存
        strategyCache.put(strategy.getId(), strategy);
        
        // 更新计数器
        probe.incrementCounterAmount("StrategyCreateCounter", 1);
        
        // 记录日志
        probe.info("Strategy created: " + strategy.getName() + " (ID: " + strategy.getId() + ")");
    }

    /**
     * 更新策略
     */
    private void updateStrategy(TrackingStrategy strategy) {
        if (strategy == null || strategy.getId() == null) {
            probe.error("Invalid strategy for update");
            return;
        }
        
        // 检查策略是否存在
        if (!strategyCache.containsKey(strategy.getId())) {
            probe.warn("Strategy not found for update: " + strategy.getId());
            return;
        }
        
        // 更新缓存
        strategyCache.put(strategy.getId(), strategy);
        
        // 更新计数器
        probe.incrementCounterAmount("StrategyUpdateCounter", 1);
        
        // 记录日志
        probe.info("Strategy updated: " + strategy.getName() + " (ID: " + strategy.getId() + ")");
    }

    /**
     * 删除策略
     */
    private void deleteStrategy(Integer strategyId) {
        if (strategyId == null) {
            probe.error("Invalid strategy ID for deletion");
            return;
        }
        
        // 检查策略是否存在
        if (!strategyCache.containsKey(strategyId)) {
            probe.warn("Strategy not found for deletion: " + strategyId);
            return;
        }
        
        // 从缓存中移除
        TrackingStrategy removedStrategy = strategyCache.remove(strategyId);
        
        // 更新计数器
        probe.incrementCounterAmount("StrategyDeleteCounter", 1);
        
        // 记录日志
        probe.info("Strategy deleted: " + removedStrategy.getName() + " (ID: " + strategyId + ")");
    }

    /**
     * 激活策略
     */
    private void activateStrategy(Integer strategyId) {
        if (strategyId == null) {
            probe.error("Invalid strategy ID for activation");
            return;
        }
        
        // 检查策略是否存在
        if (!strategyCache.containsKey(strategyId)) {
            probe.warn("Strategy not found for activation: " + strategyId);
            return;
        }
        
        // 更新状态
        TrackingStrategy strategy = strategyCache.get(strategyId);
        strategy.setStatus(TrackingStrategy.StrategyStatus.ACTIVE.name());
        
        // 记录日志
        probe.info("Strategy activated: " + strategy.getName() + " (ID: " + strategyId + ")");
    }

    /**
     * 停用策略
     */
    private void deactivateStrategy(Integer strategyId) {
        if (strategyId == null) {
            probe.error("Invalid strategy ID for deactivation");
            return;
        }
        
        // 检查策略是否存在
        if (!strategyCache.containsKey(strategyId)) {
            probe.warn("Strategy not found for deactivation: " + strategyId);
            return;
        }
        
        // 更新状态
        TrackingStrategy strategy = strategyCache.get(strategyId);
        strategy.setStatus(TrackingStrategy.StrategyStatus.INACTIVE.name());
        
        // 记录日志
        probe.info("Strategy deactivated: " + strategy.getName() + " (ID: " + strategyId + ")");
    }

    /**
     * 暂停策略
     */
    private void pauseStrategy(Integer strategyId) {
        if (strategyId == null) {
            probe.error("Invalid strategy ID for pause");
            return;
        }
        
        // 检查策略是否存在
        if (!strategyCache.containsKey(strategyId)) {
            probe.warn("Strategy not found for pause: " + strategyId);
            return;
        }
        
        // 更新状态
        TrackingStrategy strategy = strategyCache.get(strategyId);
        strategy.setStatus(TrackingStrategy.StrategyStatus.PAUSED.name());
        
        // 记录日志
        probe.info("Strategy paused: " + strategy.getName() + " (ID: " + strategyId + ")");
    }

    /**
     * 更新策略仪表盘
     */
    private void updateStrategyGauges() {
        long activeCount = strategyCache.values().stream()
                .filter(s -> TrackingStrategy.StrategyStatus.ACTIVE.name().equals(s.getStatus()))
                .count();
        
        long inactiveCount = strategyCache.values().stream()
                .filter(s -> TrackingStrategy.StrategyStatus.INACTIVE.name().equals(s.getStatus()))
                .count();
        
        long pausedCount = strategyCache.values().stream()
                .filter(s -> TrackingStrategy.StrategyStatus.PAUSED.name().equals(s.getStatus()))
                .count();
        
        probe.updateGauge("ActiveStrategies", activeCount);
        probe.updateGauge("InactiveStrategies", inactiveCount);
        probe.updateGauge("PausedStrategies", pausedCount);
    }

    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
}
