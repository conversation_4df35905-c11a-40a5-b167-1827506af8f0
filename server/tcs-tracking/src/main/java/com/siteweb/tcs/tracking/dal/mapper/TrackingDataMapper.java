package com.siteweb.tcs.tracking.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.tracking.dal.entity.TrackingData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 埋点数据Mapper接口
 */
@Mapper
public interface TrackingDataMapper extends BaseMapper<TrackingData> {

    /**
     * 查询埋点数据
     */
    @Select("<script>" +
            "SELECT * FROM tcs_tracking_data WHERE 1=1 " +
            "<if test='strategyId != null'>AND strategy_id = #{strategyId} </if>" +
            "<if test='pointId != null'>AND point_id = #{pointId} </if>" +
            "<if test='startTime != null'>AND timestamp &gt;= #{startTime} </if>" +
            "<if test='endTime != null'>AND timestamp &lt;= #{endTime} </if>" +
            "<if test='sourceId != null'>AND source_id = #{sourceId} </if>" +
            "<if test='userId != null'>AND user_id = #{userId} </if>" +
            "ORDER BY timestamp DESC " +
            "<if test='limit != null'>LIMIT #{limit} </if>" +
            "</script>")
    List<TrackingData> queryData(@Param("strategyId") Integer strategyId,
                                @Param("pointId") Integer pointId,
                                @Param("startTime") LocalDateTime startTime,
                                @Param("endTime") LocalDateTime endTime,
                                @Param("sourceId") String sourceId,
                                @Param("userId") String userId,
                                @Param("limit") Integer limit);

    /**
     * 统计埋点数据数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM tcs_tracking_data WHERE 1=1 " +
            "<if test='strategyId != null'>AND strategy_id = #{strategyId} </if>" +
            "<if test='pointId != null'>AND point_id = #{pointId} </if>" +
            "<if test='startTime != null'>AND timestamp &gt;= #{startTime} </if>" +
            "<if test='endTime != null'>AND timestamp &lt;= #{endTime} </if>" +
            "<if test='sourceId != null'>AND source_id = #{sourceId} </if>" +
            "<if test='userId != null'>AND user_id = #{userId} </if>" +
            "</script>")
    long countData(@Param("strategyId") Integer strategyId,
                  @Param("pointId") Integer pointId,
                  @Param("startTime") LocalDateTime startTime,
                  @Param("endTime") LocalDateTime endTime,
                  @Param("sourceId") String sourceId,
                  @Param("userId") String userId);

    /**
     * 获取来源分布
     */
    @Select("<script>" +
            "SELECT source_id, COUNT(*) as count FROM tcs_tracking_data WHERE 1=1 " +
            "<if test='strategyId != null'>AND strategy_id = #{strategyId} </if>" +
            "<if test='pointId != null'>AND point_id = #{pointId} </if>" +
            "<if test='startTime != null'>AND timestamp &gt;= #{startTime} </if>" +
            "<if test='endTime != null'>AND timestamp &lt;= #{endTime} </if>" +
            "GROUP BY source_id ORDER BY count DESC" +
            "</script>")
    List<Map<String, Object>> getSourceDistribution(@Param("strategyId") Integer strategyId,
                                                  @Param("pointId") Integer pointId,
                                                  @Param("startTime") LocalDateTime startTime,
                                                  @Param("endTime") LocalDateTime endTime);

    /**
     * 获取用户分布
     */
    @Select("<script>" +
            "SELECT user_id, COUNT(*) as count FROM tcs_tracking_data WHERE 1=1 " +
            "<if test='strategyId != null'>AND strategy_id = #{strategyId} </if>" +
            "<if test='pointId != null'>AND point_id = #{pointId} </if>" +
            "<if test='startTime != null'>AND timestamp &gt;= #{startTime} </if>" +
            "<if test='endTime != null'>AND timestamp &lt;= #{endTime} </if>" +
            "GROUP BY user_id ORDER BY count DESC" +
            "</script>")
    List<Map<String, Object>> getUserDistribution(@Param("strategyId") Integer strategyId,
                                                @Param("pointId") Integer pointId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);

    /**
     * 获取时间分布
     */
    @Select("<script>" +
            "SELECT DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as hour, COUNT(*) as count " +
            "FROM tcs_tracking_data WHERE 1=1 " +
            "<if test='strategyId != null'>AND strategy_id = #{strategyId} </if>" +
            "<if test='pointId != null'>AND point_id = #{pointId} </if>" +
            "<if test='startTime != null'>AND timestamp &gt;= #{startTime} </if>" +
            "<if test='endTime != null'>AND timestamp &lt;= #{endTime} </if>" +
            "GROUP BY hour ORDER BY hour" +
            "</script>")
    List<Map<String, Object>> getTimeDistribution(@Param("strategyId") Integer strategyId,
                                                @Param("pointId") Integer pointId,
                                                @Param("startTime") LocalDateTime startTime,
                                                @Param("endTime") LocalDateTime endTime);
}
