package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.resource.RedisResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.Topic;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

/**
 * 键值存储服务类
 * 提供键值存储操作的通用方法，支持批处理优化和发布订阅功能
 * 注意：发布订阅功能采用按需初始化策略，只有在实际使用时才会创建相关资源
 */
public class KeyValueStoreService extends BaseService {

    private static final Logger logger = LoggerFactory.getLogger(KeyValueStoreService.class);

    private final RedisTemplate<String, Object> redisTemplate;

    // 批处理相关属性
    private final ScheduledExecutorService executorService;
    private final AtomicReference<Map<String, String>> batchItems = new AtomicReference<>(new HashMap<>());
    private final AtomicReference<Date> lastActionTime = new AtomicReference<>(new Date());
    private ScheduledFuture<?> scheduledFuture;

    // 批处理阈值配置
    private final int batchThreshold; // 批处理阈值，默认为100条
    private final long timeThresholdMs; // 时间阈值，默认为3000毫秒

    // 发布订阅相关属性
    private RedisMessageListenerContainer messageListenerContainer;
    private final Map<String, MessageListener> channelListeners = new ConcurrentHashMap<>();
    private final Map<String, MessageListener> patternListeners = new ConcurrentHashMap<>();

    /**
     * 构造函数
     *
     * @param id 服务ID
     * @param name 服务名称
     * @param description 服务描述
     * @param resource 关联的资源
     * @param config 服务配置
     */
    public KeyValueStoreService(String id, String name, String description, Resource resource, Map<String, Object> config) {
        super(id, "keyvaluestore", name, description, resource);

        // 验证资源类型
        if (!(resource instanceof RedisResource)) {
            throw new IllegalArgumentException("资源类型不匹配，期望RedisResource，实际为" + resource.getClass().getName());
        }

        // 获取RedisTemplate
        RedisResource redisResource = (RedisResource) resource;
        this.redisTemplate = redisResource.getRedisTemplate();

        // 从配置中获取批处理阈值，默认为100
        int configBatchThreshold = 100;
        if (config != null && config.containsKey("batchThreshold")) {
            Object batchThresholdObj = config.get("batchThreshold");
            if (batchThresholdObj != null) {
                try {
                    int value = Integer.parseInt(batchThresholdObj.toString());
                    if (value > 0) {
                        configBatchThreshold = value;
                    } else {
                        logger.warn("批处理阈值必须大于0，使用默认值: 100");
                    }
                } catch (NumberFormatException e) {
                    logger.warn("批处理阈值格式不正确，使用默认值: 100");
                }
            }
        }
        this.batchThreshold = configBatchThreshold;

        // 从配置中获取时间阈值，默认为3000毫秒
        long configTimeThresholdMs = 3000;
        if (config != null && config.containsKey("timeThresholdMs")) {
            Object timeThresholdObj = config.get("timeThresholdMs");
            if (timeThresholdObj != null) {
                try {
                    long value = Long.parseLong(timeThresholdObj.toString());
                    if (value > 0) {
                        configTimeThresholdMs = value;
                    } else {
                        logger.warn("时间阈值必须大于0，使用默认值: 3000");
                    }
                } catch (NumberFormatException e) {
                    logger.warn("时间阈值格式不正确，使用默认值: 3000");
                }
            }
        }
        this.timeThresholdMs = configTimeThresholdMs;

        logger.info("键值存储服务批处理配置: 批处理阈值={}, 时间阈值={}ms", this.batchThreshold, this.timeThresholdMs);

        // 初始化批处理执行器
        this.executorService = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "KeyValueStore-BatchProcessor-" + id);
            thread.setDaemon(true);
            return thread;
        });
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化键值存储服务: {}", getId());
        // 初始化阶段不需要特殊操作
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动键值存储服务: {}", getId());
        try {
            // 测试连接
            if (redisTemplate == null) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_CONFIG_INVALID,
                    "Redis模板未初始化"
                );
            }

            // 测试连接
            Boolean result = redisTemplate.execute((org.springframework.data.redis.core.RedisCallback<Boolean>) connection -> {
                try {
                    return connection.ping() != null;
                } catch (Exception e) {
                    logger.error("测试Redis连接失败", e);
                    return false;
                }
            });

            if (!Boolean.TRUE.equals(result)) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.DATABASE_CONNECTION_FAILED,
                    "Redis连接测试失败"
                );
            }

            // 注意：不再在启动时初始化消息监听器容器，而是在需要时按需创建

            logger.info("键值存储服务启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动键值存储服务失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_START_FAILED,
                "启动键值存储服务失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 初始化消息监听器容器
     *
     * @return 初始化是否成功
     */
    private boolean initMessageListenerContainer() {
        if (messageListenerContainer == null) {
            RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
            if (connectionFactory != null) {
                try {
                    messageListenerContainer = new RedisMessageListenerContainer();
                    messageListenerContainer.setConnectionFactory(connectionFactory);

                    // 启动容器
                    messageListenerContainer.afterPropertiesSet();
                    messageListenerContainer.start();

                    logger.debug("消息监听器容器初始化成功");
                    return true;
                } catch (Exception e) {
                    logger.error("初始化消息监听器容器失败", e);
                    messageListenerContainer = null;
                    return false;
                }
            } else {
                logger.warn("无法初始化消息监听器容器：连接工厂为空");
                return false;
            }
        }
        return true; // 容器已存在，无需初始化
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止键值存储服务: {}", getId());
        try {
            // 停止批处理执行器
            cancelScheduledFuture();
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }

            // 刷新剩余的批处理项
            flushBatch(true);

            // 停止消息监听器容器
            stopMessageListenerContainer();

            logger.info("键值存储服务停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止键值存储服务失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_STOP_FAILED,
                "停止键值存储服务失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 停止消息监听器容器
     */
    private void stopMessageListenerContainer() {
        if (messageListenerContainer != null) {
            try {
                // 清空所有监听器
                channelListeners.clear();
                patternListeners.clear();

                // 停止容器
                messageListenerContainer.stop();
                messageListenerContainer = null;

                logger.debug("消息监听器容器已停止");
            } catch (Exception e) {
                logger.error("停止消息监听器容器失败", e);
            }
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁键值存储服务: {}", getId());
        // 销毁阶段不需要特殊操作，资源会自行处理连接池的关闭
        logger.info("键值存储服务销毁成功: {}", getId());
    }

    @Override
    public HealthStatus checkHealth() {
        // 委托给资源进行健康检查
        return resource.checkHealth();
    }



    /**
     * 设置键值
     *
     * @param key 键
     * @param value 值
     */
    public void set(String key, String value) {
        // 添加到批处理队列
        addToBatch(key, value);
    }

    /**
     * 批量设置键值
     *
     * @param map 键值映射
     */
    public void multiSet(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return;
        }

        // 添加到批处理队列
        map.forEach(this::addToBatch);
    }

    /**
     * 设置键值并设置过期时间
     * 注意：此方法不使用批处理，直接执行
     *
     * @param key 键
     * @param value 值
     * @param seconds 过期时间（秒）
     */
    public void setex(String key, String value, long seconds) {
        redisTemplate.opsForValue().set(key, (Object)value, seconds, TimeUnit.SECONDS);
    }

    /**
     * 获取键值
     *
     * @param key 键
     * @return 值
     */
    public String get(String key) {
        // 先刷新批处理队列，确保获取最新值
        flushBatch(true);
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 删除键
     *
     * @param key 键
     * @return 是否成功删除
     */
    public Boolean delete(String key) {
        // 先从批处理队列中移除
        batchItems.get().remove(key);
        return redisTemplate.delete(key);
    }

    /**
     * 批量删除键
     *
     * @param keys 键集合
     * @return 删除的键数量
     */
    public Long delete(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return 0L;
        }

        // 先从批处理队列中移除
        Map<String, String> currentBatch = batchItems.get();
        keys.forEach(currentBatch::remove);

        return redisTemplate.delete(keys);
    }

    /**
     * 检查键是否存在
     *
     * @param key 键
     * @return 是否存在
     */
    public Boolean hasKey(String key) {
        // 先检查批处理队列
        if (batchItems.get().containsKey(key)) {
            return true;
        }
        return redisTemplate.hasKey(key);
    }

    /**
     * 设置键的过期时间
     *
     * @param key 键
     * @param seconds 过期时间（秒）
     * @return 是否成功
     */
    public Boolean expire(String key, long seconds) {
        return redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
    }

    /**
     * 获取键的剩余过期时间
     *
     * @param key 键
     * @return 剩余过期时间（秒）
     */
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 获取所有匹配的键
     *
     * @param pattern 匹配模式
     * @return 匹配的键集合
     */
    public Set<String> keys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 获取数据库大小
     *
     * @return 数据库大小
     */
    public Long dbSize() {
        return redisTemplate.execute((org.springframework.data.redis.core.RedisCallback<Long>) connection -> connection.serverCommands().dbSize());
    }

    /**
     * 添加到批处理队列
     *
     * @param key 键
     * @param value 值
     */
    private void addToBatch(String key, String value) {
        // 取消当前的定时任务
        cancelScheduledFuture();

        // 获取当前时间
        Date currentTime = new Date();
        long timeSinceLastAction = currentTime.getTime() - lastActionTime.get().getTime();

        // 如果距离上次操作超过时间阈值，直接刷新批处理队列
        if (timeSinceLastAction > timeThresholdMs) {
            flushBatch(true);
        }

        // 添加到批处理队列
        batchItems.get().put(key, value);

        // 如果批处理队列大小达到阈值，直接刷新
        if (batchItems.get().size() >= batchThreshold) {
            flushBatch(false);
        } else {
            // 否则，设置定时任务在时间阈值后刷新
            scheduledFuture = executorService.schedule(() -> flushBatch(false), timeThresholdMs, TimeUnit.MILLISECONDS);
        }

        // 更新最后操作时间
        lastActionTime.set(currentTime);
    }

    /**
     * 刷新批处理队列
     *
     * @param force 是否强制刷新
     */
    private void flushBatch(boolean force) {
        Map<String, String> currentBatch = batchItems.get();

        // 如果强制刷新或者批处理队列大小达到阈值，执行批量写入
        if (force || currentBatch.size() >= batchThreshold) {
            if (!currentBatch.isEmpty()) {
                try {
                    // 将Map<String, String>转换为Map<String, Object>
                    Map<String, Object> objectMap = new HashMap<>(currentBatch);
                    redisTemplate.opsForValue().multiSet(objectMap);
                    logger.debug("批量写入 {} 条数据", currentBatch.size());
                } catch (Exception e) {
                    logger.error("批量写入失败", e);
                } finally {
                    // 重置批处理队列
                    batchItems.set(new HashMap<>());
                    lastActionTime.set(new Date());
                }
            }
        }
    }

    /**
     * 取消定时任务
     */
    private void cancelScheduledFuture() {
        if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
            scheduledFuture.cancel(false);
            scheduledFuture = null;
        }
    }

    /**
     * 发布消息到指定频道
     *
     * @param channel 频道名称
     * @param message 消息内容
     */
    public void publish(String channel, String message) {
        if (channel == null || channel.isEmpty()) {
            throw new IllegalArgumentException("频道名称不能为空");
        }
        if (message == null) {
            throw new IllegalArgumentException("消息内容不能为null");
        }

        try {
            redisTemplate.convertAndSend(channel, message);
            logger.debug("消息已发布到频道 {}: {}", channel, message);
        } catch (Exception e) {
            logger.error("发布消息到频道 {} 失败: {}", channel, e.getMessage(), e);
            throw new RuntimeException("发布消息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 订阅指定频道
     *
     * @param channel 频道名称
     * @param messageHandler 消息处理器，接收消息内容字符串
     * @return 是否成功订阅
     */
    public boolean subscribe(String channel, Consumer<String> messageHandler) {
        if (channel == null || channel.isEmpty()) {
            throw new IllegalArgumentException("频道名称不能为空");
        }
        if (messageHandler == null) {
            throw new IllegalArgumentException("消息处理器不能为null");
        }

        // 按需初始化消息监听器容器
        if (!initMessageListenerContainer()) {
            logger.error("无法订阅频道 {}：消息监听器容器初始化失败", channel);
            return false;
        }

        try {
            // 创建消息监听器
            MessageListener listener = (message, pattern) -> {
                try {
                    // 使用StringRedisSerializer解析消息内容
                    RedisSerializer<String> serializer = new StringRedisSerializer();
                    String messageContent = serializer.deserialize(message.getBody());

                    logger.info("收到来自频道 {} 的原始消息: {}", channel, messageContent);

                    // 调用处理器
                    messageHandler.accept(messageContent);
                } catch (Exception e) {
                    logger.error("处理来自频道 {} 的消息时发生错误: {}", channel, e.getMessage(), e);
                }
            };

            // 注册监听器
            Topic topic = new ChannelTopic(channel);
            messageListenerContainer.addMessageListener(listener, topic);

            // 保存监听器引用，以便后续取消订阅
            channelListeners.put(channel, listener);

            logger.debug("已成功订阅频道: {}", channel);
            return true;
        } catch (Exception e) {
            logger.error("订阅频道 {} 失败: {}", channel, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 订阅匹配指定模式的频道
     *
     * @param pattern 频道模式，例如 "channel.*"
     * @param messageHandler 消息处理器，接收消息内容字符串和频道名称
     * @return 是否成功订阅
     */
    public boolean psubscribe(String pattern, BiConsumer<String, String> messageHandler) {
        if (pattern == null || pattern.isEmpty()) {
            throw new IllegalArgumentException("频道模式不能为空");
        }
        if (messageHandler == null) {
            throw new IllegalArgumentException("消息处理器不能为null");
        }

        // 按需初始化消息监听器容器
        if (!initMessageListenerContainer()) {
            logger.error("无法订阅模式 {}：消息监听器容器初始化失败", pattern);
            return false;
        }

        try {
            // 创建消息监听器
            MessageListener listener = (message, patternBytes) -> {
                try {
                    // 使用StringRedisSerializer解析消息内容和频道
                    RedisSerializer<String> serializer = new StringRedisSerializer();
                    String messageContent = serializer.deserialize(message.getBody());
                    String channel = serializer.deserialize(message.getChannel());

                    logger.info("收到匹配模式 {} 的原始消息: 频道={}, 内容={}", pattern, channel, messageContent);

                    // 调用处理器
                    messageHandler.accept(messageContent, channel);
                } catch (Exception e) {
                    logger.error("处理匹配模式 {} 的消息时发生错误: {}", pattern, e.getMessage(), e);
                }
            };

            // 注册监听器
            Topic topic = new PatternTopic(pattern);
            messageListenerContainer.addMessageListener(listener, topic);

            // 保存监听器引用，以便后续取消订阅
            patternListeners.put(pattern, listener);

            logger.debug("已成功订阅模式: {}", pattern);
            return true;
        } catch (Exception e) {
            logger.error("订阅模式 {} 失败: {}", pattern, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 取消订阅指定频道
     *
     * @param channel 频道名称
     * @return 是否成功取消订阅
     */
    public boolean unsubscribe(String channel) {
        if (channel == null || channel.isEmpty()) {
            throw new IllegalArgumentException("频道名称不能为空");
        }

        if (messageListenerContainer == null) {
            logger.warn("无法取消订阅频道 {}：消息监听器容器未初始化", channel);
            return false;
        }

        try {
            // 获取监听器
            MessageListener listener = channelListeners.get(channel);
            if (listener != null) {
                // 移除监听器
                messageListenerContainer.removeMessageListener(listener);
                channelListeners.remove(channel);
                logger.debug("已取消订阅频道: {}", channel);
                return true;
            } else {
                logger.warn("未找到频道 {} 的监听器", channel);
                return false;
            }
        } catch (Exception e) {
            logger.error("取消订阅频道 {} 失败: {}", channel, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 取消订阅指定模式
     *
     * @param pattern 频道模式
     * @return 是否成功取消订阅
     */
    public boolean punsubscribe(String pattern) {
        if (pattern == null || pattern.isEmpty()) {
            throw new IllegalArgumentException("频道模式不能为空");
        }

        if (messageListenerContainer == null) {
            logger.warn("无法取消订阅模式 {}：消息监听器容器未初始化", pattern);
            return false;
        }

        try {
            // 获取监听器
            MessageListener listener = patternListeners.get(pattern);
            if (listener != null) {
                // 移除监听器
                messageListenerContainer.removeMessageListener(listener);
                patternListeners.remove(pattern);
                logger.debug("已取消订阅模式: {}", pattern);
                return true;
            } else {
                logger.warn("未找到模式 {} 的监听器", pattern);
                return false;
            }
        } catch (Exception e) {
            logger.error("取消订阅模式 {} 失败: {}", pattern, e.getMessage(), e);
            return false;
        }
    }
}
