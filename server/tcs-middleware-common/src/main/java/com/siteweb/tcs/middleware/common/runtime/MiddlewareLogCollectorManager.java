package com.siteweb.tcs.middleware.common.runtime;

import ch.qos.logback.classic.LoggerContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 中间件日志收集器管理器
 * 统一管理Resource和Service的日志收集器
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Component
public class MiddlewareLogCollectorManager {
    
    private static final Logger logger = LoggerFactory.getLogger(MiddlewareLogCollectorManager.class);
    
    private final ConcurrentMap<String, MiddlewareLogCollector> resourceLogCollectors = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, MiddlewareLogCollector> serviceLogCollectors = new ConcurrentHashMap<>();
    
    /**
     * 为Resource创建日志收集器
     */
    public MiddlewareLogCollector createResourceLogCollector(String resourceId) {
        String packageName = "com.siteweb.tcs.middleware";
        MiddlewareLogCollector collector = new MiddlewareLogCollector(resourceId, "resource", packageName);
        
        // 配置并启动收集器
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        collector.setContext(loggerContext);
        collector.start();
        
        // 添加到Logback配置
        ch.qos.logback.classic.Logger rootLogger = loggerContext.getLogger(ch.qos.logback.classic.Logger.ROOT_LOGGER_NAME);
        rootLogger.addAppender(collector);
        
        resourceLogCollectors.put(resourceId, collector);
        logger.info("cmcc-h2-config-primary: {}", resourceId);
        
        // 添加初始化日志
        collector.addLifecycleLog("INFO", "Resource log collector initialized for: " + resourceId);
        
        return collector;
    }
    
    /**
     * 为Service创建日志收集器
     */
    public MiddlewareLogCollector createServiceLogCollector(String serviceId) {
        String packageName = "com.siteweb.tcs.middleware";
        MiddlewareLogCollector collector = new MiddlewareLogCollector(serviceId, "service", packageName);
        
        // 配置并启动收集器
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        collector.setContext(loggerContext);
        collector.start();
        
        // 添加到Logback配置
        ch.qos.logback.classic.Logger rootLogger = loggerContext.getLogger(ch.qos.logback.classic.Logger.ROOT_LOGGER_NAME);
        rootLogger.addAppender(collector);
        
        serviceLogCollectors.put(serviceId, collector);
        logger.info("Created log collector for service: {}", serviceId);
        
        // 添加初始化日志
        collector.addLifecycleLog("INFO", "Service log collector initialized for: " + serviceId);
        
        return collector;
    }
    
    /**
     * 获取Resource的日志收集器
     */
    public MiddlewareLogCollector getResourceLogCollector(String resourceId) {
        return resourceLogCollectors.get(resourceId);
    }
    
    /**
     * 获取Service的日志收集器
     */
    public MiddlewareLogCollector getServiceLogCollector(String serviceId) {
        return serviceLogCollectors.get(serviceId);
    }
    
    /**
     * 获取Resource的日志流
     */
    public SseEmitter getResourceLogStream(String resourceId) {
        MiddlewareLogCollector collector = resourceLogCollectors.get(resourceId);
        if (collector == null) {
            logger.warn("Log collector not found for resource: {}", resourceId);
            return null;
        }
        return collector.getStream();
    }
    
    /**
     * 获取Service的日志流
     */
    public SseEmitter getServiceLogStream(String serviceId) {
        MiddlewareLogCollector collector = serviceLogCollectors.get(serviceId);
        if (collector == null) {
            logger.warn("Log collector not found for service: {}", serviceId);
            return null;
        }
        return collector.getStream();
    }
    
    /**
     * 移除Resource的日志收集器
     */
    public void removeResourceLogCollector(String resourceId) {
        MiddlewareLogCollector collector = resourceLogCollectors.remove(resourceId);
        if (collector != null) {
            // 添加销毁日志
            collector.addLifecycleLog("INFO", "Resource log collector destroyed for: " + resourceId);
            
            // 从Logback配置中移除
            LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
            ch.qos.logback.classic.Logger rootLogger = loggerContext.getLogger(ch.qos.logback.classic.Logger.ROOT_LOGGER_NAME);
            rootLogger.detachAppender(collector);
            
            collector.stop();
            logger.info("Removed log collector for resource: {}", resourceId);
        }
    }
    
    /**
     * 移除Service的日志收集器
     */
    public void removeServiceLogCollector(String serviceId) {
        MiddlewareLogCollector collector = serviceLogCollectors.remove(serviceId);
        if (collector != null) {
            // 添加销毁日志
            collector.addLifecycleLog("INFO", "Service log collector destroyed for: " + serviceId);
            
            // 从Logback配置中移除
            LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
            ch.qos.logback.classic.Logger rootLogger = loggerContext.getLogger(ch.qos.logback.classic.Logger.ROOT_LOGGER_NAME);
            rootLogger.detachAppender(collector);
            
            collector.stop();
            logger.info("Removed log collector for service: {}", serviceId);
        }
    }
    
    /**
     * 记录Resource生命周期日志
     */
    public void logResourceLifecycle(String resourceId, String level, String message) {
        MiddlewareLogCollector collector = resourceLogCollectors.get(resourceId);
        if (collector != null) {
            collector.addLifecycleLog(level, message);
        }
    }
    
    /**
     * 记录Service生命周期日志
     */
    public void logServiceLifecycle(String serviceId, String level, String message) {
        MiddlewareLogCollector collector = serviceLogCollectors.get(serviceId);
        if (collector != null) {
            collector.addLifecycleLog(level, message);
        }
    }

    /**
     * 获取Resource的最近日志
     */
    public MiddlewareLogCollector.LogInfo[] getResourceRecentLogs(String resourceId, int count) {
        MiddlewareLogCollector collector = resourceLogCollectors.get(resourceId);
        if (collector == null) {
            return new MiddlewareLogCollector.LogInfo[]{};
        }
        return collector.getRecentLogs(count);
    }

    /**
     * 获取Service的最近日志
     */
    public MiddlewareLogCollector.LogInfo[] getServiceRecentLogs(String serviceId, int count) {
        MiddlewareLogCollector collector = serviceLogCollectors.get(serviceId);
        if (collector == null) {
            return new MiddlewareLogCollector.LogInfo[]{};
        }
        return collector.getRecentLogs(count);
    }
}
