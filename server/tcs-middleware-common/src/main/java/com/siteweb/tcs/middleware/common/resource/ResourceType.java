package com.siteweb.tcs.middleware.common.resource;

/**
 * 资源类型枚举
 * 用于标识不同类型的资源
 */
public enum ResourceType {
    /**
     * MySQL数据库
     */
    MYSQL("MYSQL", "RELATIONAL_DB"),

    /**
     * H2数据库
     */
    H2("H2", "RELATIONAL_DB"),

    /**
     * PostgreSQL数据库
     */
    POSTGRESQL("POSTGRESQL", "RELATIONAL_DB"),

    /**
     * Redis键值存储
     */
    REDIS("REDIS", "KEY_VALUE_STORE"),

    /**
     * Kafka消息队列
     */
    KAFKA("KAFKA", "MESSAGE_QUEUE"),

    /**
     * MQTT消息队列
     */
    MQTT("MQTT", "MESSAGE_QUEUE"),

    /**
     * HTTP服务器
     */
    HTTP_SERVER("HTTP_SERVER", "WEB_SERVER");

    private final String code;
    private final String category;

    ResourceType(String code, String category) {
        this.code = code;
        this.category = category;
    }

    /**
     * 获取资源类型编码
     *
     * @return 资源类型编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取资源类别
     *
     * @return 资源类别
     */
    public String getCategory() {
        return category;
    }

    /**
     * 根据编码获取资源类型
     *
     * @param code 资源类型编码
     * @return 资源类型，如果不存在则返回null
     */
    public static ResourceType fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (ResourceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取资源类型（忽略大小写）
     *
     * @param code 资源类型编码
     * @return 资源类型，如果不存在则返回null
     */
    public static ResourceType fromCodeIgnoreCase(String code) {
        if (code == null) {
            return null;
        }

        for (ResourceType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断资源类型是否属于指定类别
     *
     * @param category 资源类别
     * @return 是否属于指定类别
     */
    public boolean isCategory(String category) {
        return this.category.equals(category);
    }
}
