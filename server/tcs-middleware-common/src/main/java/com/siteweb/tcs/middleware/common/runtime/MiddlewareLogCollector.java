package com.siteweb.tcs.middleware.common.runtime;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Objects;

/**
 * 中间件日志收集器
 * 用于收集Resource和Service的生命周期日志
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MiddlewareLogCollector extends AppenderBase<ILoggingEvent> {
    private final MiddlewareSseEmitterCollection emitters = new MiddlewareSseEmitterCollection();
    private final LinkedList<LogInfo> list = new LinkedList<>();
    private int maxSize = 200;
    private String componentId;
    private String componentType; // "resource" or "service"
    private String packageName;
    private PatternLayoutEncoder encoder;

    public MiddlewareLogCollector(String componentId, String componentType, String packageName) {
        this.componentId = componentId;
        this.componentType = componentType;
        this.packageName = packageName;
    }

    public void setMaxSize(int maxSize) {
        this.maxSize = maxSize;
        while (list.size() > maxSize) {
            list.removeFirst();
        }
    }

    public LogInfo[] getLogs() {
        return list.toArray(new LogInfo[]{});
    }

    /**
     * 获取最近的指定数量的日志
     *
     * @param count 日志数量
     * @return 最近的日志数组
     */
    public LogInfo[] getRecentLogs(int count) {
        if (count <= 0) {
            return new LogInfo[]{};
        }

        int size = list.size();
        if (size <= count) {
            return list.toArray(new LogInfo[]{});
        }

        // 获取最后count条日志
        LogInfo[] result = new LogInfo[count];
        for (int i = 0; i < count; i++) {
            result[i] = list.get(size - count + i);
        }
        return result;
    }

    public SseEmitter getStream() {
        SseEmitter emitter = new SseEmitter();
        emitter.onCompletion(() -> emitters.remove(emitter));
        emitter.onTimeout(() -> emitters.remove(emitter));
        emitter.onError(e -> emitters.remove(emitter));
        
        // 根据日志级别过滤
        LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
        Logger logger = loggerContext.getLogger(packageName);
        Level level = logger.getLevel();
        
        // 根据日志等级过滤
        if (level != null && !Objects.equals(level.levelStr, "ALL")) {
            emitters.connected(emitter, list.stream().filter(e -> e.getLevel().equals(level.levelStr)).toArray());
        } else {
            emitters.connected(emitter, list);
        }
        return emitter;
    }

    @Override
    public void start() {
        if(encoder == null){
            encoder = new PatternLayoutEncoder();
            encoder.setContext(this.getContext());
            encoder.setPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n");
            encoder.start();
        }
        list.clear();
        super.start();
        emitters.clean();
    }

    @Override
    public void stop() {
        super.stop();
        if (encoder != null) {
            encoder.stop();
            encoder = null;
        }
    }

    @Override
    protected void append(ILoggingEvent eventObject) {
        if (!started) return;
        
        // 检查日志是否来自相关的包或包含组件ID
        StackTraceElement[] steArray = new Throwable().getStackTrace();
        boolean hit = Arrays.stream(steArray).anyMatch(e -> e.getClassName().startsWith(packageName)) ||
                     eventObject.getMessage().contains(componentId) ||
                     eventObject.getLoggerName().contains("middleware");
        
        if (hit) {
            LogInfo info = new LogInfo();
            info.setTimeStamp(LocalDateTime.ofInstant(Instant.ofEpochMilli(eventObject.getTimeStamp()), ZoneId.systemDefault()));
            info.setMessage(new String(this.encoder.encode(eventObject)));
            info.setLevel(eventObject.getLevel().levelStr);
            info.setLoggerName(eventObject.getLoggerName());
            info.setThread(eventObject.getThreadName());
            info.setComponentId(componentId);
            info.setComponentType(componentType);
            
            list.addLast(info);
            if (list.size() > maxSize) {
                list.removeFirst();
            }
            emitters.append(info);
        }
    }

    /**
     * 手动添加生命周期日志
     */
    public void addLifecycleLog(String level, String message) {
        LogInfo info = new LogInfo();
        info.setTimeStamp(LocalDateTime.now());
        info.setMessage(message);
        info.setLevel(level);
        info.setLoggerName("middleware." + componentType);
        info.setThread(Thread.currentThread().getName());
        info.setComponentId(componentId);
        info.setComponentType(componentType);
        
        list.addLast(info);
        if (list.size() > maxSize) {
            list.removeFirst();
        }
        emitters.append(info);
    }

    @Data
    public static class LogInfo {
        private LocalDateTime timeStamp;
        private String level;
        private String loggerName;
        private String message;
        private String thread;
        private String componentId;
        private String componentType;
    }
}
