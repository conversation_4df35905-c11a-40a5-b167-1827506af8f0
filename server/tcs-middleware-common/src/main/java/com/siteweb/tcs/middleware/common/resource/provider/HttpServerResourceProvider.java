package com.siteweb.tcs.middleware.common.resource.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.model.config.HttpServerConfig;
import com.siteweb.tcs.middleware.common.resource.HttpServerResource;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * HTTP服务器资源提供者
 * 使用统一的配置转换机制
 */
@Component
public class HttpServerResourceProvider extends AbstractResourceProvider<HttpServerResource, HttpServerConfig> {

    @Override
    public String getType() {
        return ResourceType.HTTP_SERVER.getCode();
    }

    @Override
    protected Class<HttpServerConfig> getConfigClass() {
        return HttpServerConfig.class;
    }

    @Override
    protected void validateConfigObject(HttpServerConfig config) throws MiddlewareTechnicalException {
        super.validateConfigObject(config);
        
        List<String> errors = new ArrayList<>();

        // 验证主机地址
        if (!StringUtils.hasText(config.getHost())) {
            errors.add("主机地址不能为空");
        }

        // 验证端口
        if (config.getPort() <= 0 || config.getPort() > 65535) {
            errors.add("端口必须在1-65535之间");
        }

        // 验证空闲超时时间
        if (config.getIdleTimeout() <= 0) {
            errors.add("空闲超时时间必须大于0");
        }

        // 验证连接队列大小
        if (config.getBacklog() <= 0) {
            errors.add("连接队列大小必须大于0");
        }

        // 验证HTTPS配置
        if (config.isEnableHttps()) {
            if (!StringUtils.hasText(config.getCertPath())) {
                errors.add("启用HTTPS时，证书路径不能为空");
            }
            if (!StringUtils.hasText(config.getKeyPath())) {
                errors.add("启用HTTPS时，私钥路径不能为空");
            }
        }

        // 验证最大请求大小
        if (config.getMaxRequestSize() <= 0) {
            errors.add("最大请求大小必须大于0");
        }

        if (!errors.isEmpty()) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_CONFIG_INVALID,
                "HTTP服务器配置验证失败: " + String.join(", ", errors)
            );
        }
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        try {
            HttpServerConfig httpConfig = convertMapToConfig(config);
            validateConfigObject(httpConfig);
            return ValidationResult.valid();
        } catch (MiddlewareTechnicalException e) {
            logger.error("验证HTTP服务器配置失败", e);
            return ValidationResult.invalid(List.of(e.getMessage()));
        } catch (Exception e) {
            logger.error("验证HTTP服务器配置失败", e);
            return ValidationResult.invalid(List.of("配置格式错误: " + e.getMessage()));
        }
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                return ConnectionTestResult.failure("配置验证失败: " + String.join(", ", validationResult.getErrors()));
            }

            HttpServerConfig httpConfig = convertMapToConfig(config);

            // 检查端口是否可用
            try (Socket socket = new Socket()) {
                // 尝试绑定端口，如果端口已被占用，则会抛出异常
                socket.bind(new InetSocketAddress(httpConfig.getHost(), httpConfig.getPort()));

                Map<String, Object> details = new HashMap<>();
                details.put("host", httpConfig.getHost());
                details.put("port", httpConfig.getPort());
                details.put("idleTimeout", httpConfig.getIdleTimeout() + "s");
                details.put("backlog", httpConfig.getBacklog());
                details.put("enableHttps", httpConfig.isEnableHttps());

                return ConnectionTestResult.success("端口可用", details);
            } catch (Exception e) {
                return ConnectionTestResult.failure("端口已被占用: " + e.getMessage());
            }
        } catch (Exception e) {
            logger.error("测试HTTP服务器连接失败", e);
            return ConnectionTestResult.failure("连接测试异常: " + e.getMessage());
        }
    }

    @Override
    protected HttpServerResource doCreateResource(String id, String name, String description, HttpServerConfig config) 
            throws MiddlewareTechnicalException {
        try {
            logger.info("开始创建HTTP服务器资源: id={}, name={}, host={}:{}", 
                id, name, config.getHost(), config.getPort());

            // 创建HTTP服务器资源
            HttpServerResource resource = new HttpServerResource(
                id,
                getType(),
                name,
                description,
                config.getHost(),
                config.getPort(),
                config.getIdleTimeout(),
                config.getBacklog()
            );
            
            logger.info("HTTP服务器资源创建成功: id={}, name={}", id, name);
            return resource;
            
        } catch (Exception e) {
            logger.error("创建HTTP服务器资源失败: id={}, name={}", id, name, e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_INITIALIZATION_FAILED,
                "创建HTTP服务器资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected String getConfigString(HttpServerConfig config) {
        // 隐藏敏感信息
        return String.format("HttpServerConfig{host='%s', port=%d, idleTimeout=%d, enableHttps=%s, keyPassword='***'}",
            config.getHost(), config.getPort(), config.getIdleTimeout(), config.isEnableHttps());
    }

    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        if (resource instanceof HttpServerResource) {
            try {
                logger.info("开始销毁HTTP服务器资源: {}", resource.getId());
                // 调用资源的destroy方法
                resource.destroy();
                logger.info("HTTP服务器资源销毁成功: {}", resource.getId());
            } catch (Exception e) {
                logger.error("销毁HTTP服务器资源失败: {}", resource.getId(), e);
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.RESOURCE_DESTRUCTION_FAILED,
                    "销毁HTTP服务器资源失败: " + e.getMessage(),
                    e
                );
            }
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                "资源类型不匹配，期望HttpServerResource，实际为" + resource.getClass().getName()
            );
        }
    }
}
