# Stream插件中HttpServerResource的使用指南

## 概述

Stream插件中的HttpServerResource是一个重要的概念，它允许Stream插件复用中间件系统中已经配置和管理的HTTP服务器资源。这种设计避免了重复创建HTTP服务器，提供了统一的资源管理和配置。

## 设计目的

### 1. 资源复用
- **避免端口冲突**：多个Stream插件可以共享同一个HTTP服务器端口
- **统一管理**：HTTP服务器的生命周期由中间件系统统一管理
- **配置集中**：服务器配置（端口、超时等）在中间件层面统一配置

### 2. 路由动态绑定
- **灵活路由**：Stream插件可以动态绑定自定义路由到HTTP服务器
- **多租户支持**：不同的Stream插件可以绑定不同的路径
- **热更新**：支持运行时更新路由配置

### 3. 数据流集成
- **HTTP入口**：作为数据流的入口点，接收外部HTTP请求
- **消息转换**：将HTTP请求转换为Stream消息在数据流中传递
- **响应处理**：将数据流处理结果作为HTTP响应返回

## HttpServerResource核心功能

### 1. 基础服务器管理
```java
public class HttpServerResource extends BaseResource {
    // 服务器绑定
    private ServerBinding serverBinding;
    
    // 启动HTTP服务器
    protected void doStart() throws MiddlewareTechnicalException {
        Route defaultRoute = createDefaultRoute();
        CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
                .bind(defaultRoute);
        serverBinding = bindingFuture.toCompletableFuture().get();
    }
    
    // 停止HTTP服务器
    protected void doStop() throws MiddlewareTechnicalException {
        if (serverBinding != null) {
            serverBinding.terminate(Duration.ofSeconds(10));
        }
    }
}
```

### 2. 动态路由绑定
```java
// 绑定自定义路由
public CompletableFuture<ServerBinding> bindRoute(Route route) {
    // 停止当前绑定
    if (serverBinding != null) {
        serverBinding.terminate(Duration.ofSeconds(5));
    }
    
    // 创建新的绑定
    CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
            .bind(route);
    
    // 更新服务器绑定
    serverBinding = bindingFuture.toCompletableFuture().get();
    return CompletableFuture.completedFuture(serverBinding);
}
```

### 3. 自定义处理流绑定
```java
// 绑定自定义处理流
public CompletableFuture<ServerBinding> bindHandler(Flow<HttpRequest, HttpResponse, ?> handler) {
    CompletionStage<ServerBinding> bindingFuture = http.newServerAt(host, port)
            .bindFlow(handler);
    
    serverBinding = bindingFuture.toCompletableFuture().get();
    return CompletableFuture.completedFuture(serverBinding);
}
```

## HTTPServerShape的使用方式

### 1. 配置选项
```java
@Data
@EqualsAndHashCode(callSuper = true)
public class HTTPServerShapeOption extends StreamShapeOption {
    /**
     * HTTP SERVER 中间件的ID
     */
    private String middlewareId;
    
    /**
     * 自定义路由路径
     */
    private String routePath = "/api/stream";
    
    /**
     * 请求处理方式
     */
    private String handlerType = "ROUTE"; // ROUTE, FLOW, CUSTOM
    
    /**
     * 响应超时时间（秒）
     */
    private int responseTimeout = 30;
}
```

### 2. 完整的HTTPServerShape实现

```java
@Slf4j
@Shape(type = "http-server")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("server")
@ShapeAuthor("TCS Team")
@ShapeOutlet(id = 0x01, type = HTTPRequestMessage.class, desc = "HTTP请求")
@ShapeOutlet(id = 0x02, type = StateEventMessage.class, desc = "服务器状态")
public class HTTPServerShape extends AbstractShape {

    @Recoverable
    private HTTPServerShapeOption options;
    
    private HttpServerResource httpServerResource;
    private Route customRoute;

    public HTTPServerShape(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof HTTPServerShapeOption httpServerOption) {
            this.options = httpServerOption;
            if (isStarted()) {
                stopServer();
                startServer();
            }
        }
    }

    @Override
    protected void onStart() {
        if (options != null && options.isEnabled()) {
            startServer();
        }
    }

    @Override
    protected void onStop() {
        stopServer();
    }

    private void startServer() {
        try {
            // 从中间件获取HttpServerResource
            httpServerResource = getHttpServerResource(options.getMiddlewareId());
            
            if (httpServerResource == null) {
                log.error("HttpServerResource not found: {}", options.getMiddlewareId());
                return;
            }

            // 创建自定义路由
            customRoute = createCustomRoute();
            
            // 绑定路由到HTTP服务器
            httpServerResource.bindRoute(customRoute);
            
            log.info("HTTP server shape started with middleware: {}", options.getMiddlewareId());
            
            // 发送启动状态事件
            var startEvent = new StateEventMessage();
            startEvent.setPayload(true);
            startEvent.setTimestamp(LocalDateTime.now());
            context.getOutLet((short) 0x02).broadcast(startEvent);
            
        } catch (Exception e) {
            log.error("Failed to start HTTP server shape", e);
        }
    }

    private void stopServer() {
        if (httpServerResource != null) {
            try {
                // 这里可以选择重置为默认路由或者保持当前路由
                // httpServerResource.bindRoute(createDefaultRoute());
                log.info("HTTP server shape stopped");
                
                // 发送停止状态事件
                var stopEvent = new StateEventMessage();
                stopEvent.setPayload(false);
                stopEvent.setTimestamp(LocalDateTime.now());
                context.getOutLet((short) 0x02).broadcast(stopEvent);
                
            } catch (Exception e) {
                log.error("Failed to stop HTTP server shape", e);
            }
        }
    }

    private Route createCustomRoute() {
        return route(
            path(options.getRoutePath(), () ->
                route(
                    get(() -> handleGetRequest()),
                    post(() -> handlePostRequest()),
                    put(() -> handlePutRequest()),
                    delete(() -> handleDeleteRequest())
                )
            )
        );
    }

    private Route handleGetRequest() {
        return extractRequest(request -> {
            // 创建HTTP请求消息
            var httpRequestMsg = createHttpRequestMessage(request, "GET");
            
            // 广播到数据流
            context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
            
            // 返回响应
            return complete(StatusCodes.OK, "Request processed");
        });
    }

    private Route handlePostRequest() {
        return extractRequest(request -> 
            entity(Unmarshaller.entityToString(), body -> {
                // 创建HTTP请求消息
                var httpRequestMsg = createHttpRequestMessage(request, "POST");
                httpRequestMsg.setPayload(body);
                
                // 广播到数据流
                context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
                
                // 返回响应
                return complete(StatusCodes.OK, "Request processed");
            })
        );
    }

    private Route handlePutRequest() {
        return extractRequest(request -> 
            entity(Unmarshaller.entityToString(), body -> {
                var httpRequestMsg = createHttpRequestMessage(request, "PUT");
                httpRequestMsg.setPayload(body);
                context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
                return complete(StatusCodes.OK, "Request processed");
            })
        );
    }

    private Route handleDeleteRequest() {
        return extractRequest(request -> {
            var httpRequestMsg = createHttpRequestMessage(request, "DELETE");
            context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
            return complete(StatusCodes.OK, "Request processed");
        });
    }

    private HTTPRequestMessage createHttpRequestMessage(HttpRequest request, String method) {
        var httpRequestMsg = new HTTPRequestMessage();
        httpRequestMsg.setMethod(HttpMethods.lookup(method).get());
        httpRequestMsg.setUri(request.getUri().toString());
        httpRequestMsg.setHeaders(extractHeaders(request));
        httpRequestMsg.setTimestamp(LocalDateTime.now());
        return httpRequestMsg;
    }

    private Map<String, String> extractHeaders(HttpRequest request) {
        Map<String, String> headers = new HashMap<>();
        request.getHeaders().forEach(header -> 
            headers.put(header.name(), header.value())
        );
        return headers;
    }

    /**
     * 从中间件获取HttpServerResource
     */
    private HttpServerResource getHttpServerResource(String middlewareId) {
        try {
            // 通过ResourceRegistry获取中间件资源
            // 这里需要实现具体的资源获取逻辑
            // 可能需要通过Spring上下文或者其他方式获取ResourceRegistry
            
            // 示例实现（需要根据实际架构调整）
            ResourceRegistry resourceRegistry = getResourceRegistry();
            Resource resource = resourceRegistry.get(middlewareId);
            
            if (resource instanceof HttpServerResource) {
                return (HttpServerResource) resource;
            } else {
                log.error("Resource {} is not an HttpServerResource", middlewareId);
                return null;
            }
        } catch (Exception e) {
            log.error("Failed to get HttpServerResource: {}", middlewareId, e);
            return null;
        }
    }

    /**
     * 获取ResourceRegistry实例
     * 这里需要根据实际的架构实现
     */
    private ResourceRegistry getResourceRegistry() {
        // 实现方式1: 通过Spring上下文获取
        // return SpringContextHolder.getBean(ResourceRegistry.class);
        
        // 实现方式2: 通过全局上下文获取
        // return context.getGlobalContext().getResourceRegistry();
        
        // 实现方式3: 通过静态方法获取
        // return ResourceRegistryHolder.getInstance();
        
        // 临时实现，需要根据实际情况调整
        throw new UnsupportedOperationException("ResourceRegistry access not implemented");
    }

    @Override
    protected void processMessage(StreamMessage message) {
        // HTTP服务器Shape通常不处理输入消息
        // 它主要作为数据流的入口点
    }
}
```
