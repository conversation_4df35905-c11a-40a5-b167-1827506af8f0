package com.siteweb.stream.defaults.networks;

import akka.NotUsed;
import akka.http.javadsl.model.*;
import akka.http.javadsl.server.Route;
import akka.http.javadsl.unmarshalling.Unmarshaller;
import akka.stream.javadsl.Flow;
import com.siteweb.stream.core.annotation.*;
import com.siteweb.stream.core.context.ShapeRuntimeContext;
import com.siteweb.stream.core.message.EventMessage;
import com.siteweb.stream.core.message.StreamMessage;
import com.siteweb.stream.core.option.StreamShapeOption;
import com.siteweb.stream.core.shape.AbstractShape;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static akka.http.javadsl.server.Directives.*;

/**
 * HTTP服务器Shape示例实现
 * 展示如何使用中间件的HttpServerResource
 * 
 * <AUTHOR> Team
 * @date 2025-01-28
 */
@Slf4j
@Shape(type = "http-server-example")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("server")
@ShapeAuthor("TCS Team")
@ShapeDescription("HTTP服务器Shape，使用中间件HttpServerResource")
@ShapeOutlet(id = 0x01, type = HTTPRequestMessage.class, desc = "HTTP请求消息")
@ShapeOutlet(id = 0x02, type = StateEventMessage.class, desc = "服务器状态事件")
public class HTTPServerShapeExample extends AbstractShape {

    @Recoverable
    private HTTPServerShapeExampleOption options;
    
    private Object httpServerResource; // 实际类型应该是HttpServerResource
    private Route customRoute;

    public HTTPServerShapeExample(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof HTTPServerShapeExampleOption httpServerOption) {
            this.options = httpServerOption;
            if (isStarted()) {
                restartServer();
            }
        }
    }

    @Override
    protected void onStart() {
        if (options != null && options.isEnabled()) {
            startServer();
        }
    }

    @Override
    protected void onStop() {
        stopServer();
    }

    private void startServer() {
        try {
            // 获取HttpServerResource
            httpServerResource = getHttpServerResource(options.getMiddlewareId());
            
            if (httpServerResource == null) {
                log.error("HttpServerResource not found: {}", options.getMiddlewareId());
                sendStateEvent(false, "HttpServerResource not found");
                return;
            }

            // 创建自定义路由
            customRoute = createCustomRoute();
            
            // 绑定路由到HTTP服务器
            bindRouteToServer(customRoute);
            
            log.info("HTTP server shape started successfully with middleware: {}", options.getMiddlewareId());
            sendStateEvent(true, "HTTP server started");
            
        } catch (Exception e) {
            log.error("Failed to start HTTP server shape", e);
            sendStateEvent(false, "Failed to start: " + e.getMessage());
        }
    }

    private void stopServer() {
        if (httpServerResource != null) {
            try {
                log.info("HTTP server shape stopped");
                sendStateEvent(false, "HTTP server stopped");
            } catch (Exception e) {
                log.error("Failed to stop HTTP server shape", e);
            }
        }
    }

    private void restartServer() {
        stopServer();
        startServer();
    }

    /**
     * 创建自定义路由
     */
    private Route createCustomRoute() {
        return route(
            // 主路径
            path(options.getRoutePath(), () ->
                route(
                    // GET请求处理
                    get(() -> handleGetRequest()),
                    // POST请求处理
                    post(() -> handlePostRequest()),
                    // PUT请求处理
                    put(() -> handlePutRequest()),
                    // DELETE请求处理
                    delete(() -> handleDeleteRequest())
                )
            ),
            // 健康检查路径
            path(options.getRoutePath() + "/health", () ->
                get(() -> complete(StatusCodes.OK, "OK"))
            ),
            // 状态查询路径
            path(options.getRoutePath() + "/status", () ->
                get(() -> complete(StatusCodes.OK, createStatusResponse()))
            )
        );
    }

    /**
     * 处理GET请求
     */
    private Route handleGetRequest() {
        return extractRequest(request -> {
            log.debug("Received GET request: {}", request.getUri());
            
            // 创建HTTP请求消息
            HTTPRequestMessage httpRequestMsg = createHttpRequestMessage(request, "GET", "");
            
            // 广播到数据流
            context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
            
            // 返回响应
            return complete(StatusCodes.OK, createSuccessResponse("GET request processed"));
        });
    }

    /**
     * 处理POST请求
     */
    private Route handlePostRequest() {
        return extractRequest(request -> 
            entity(Unmarshaller.entityToString(), body -> {
                log.debug("Received POST request with body: {}", body);
                
                // 创建HTTP请求消息
                HTTPRequestMessage httpRequestMsg = createHttpRequestMessage(request, "POST", body);
                
                // 广播到数据流
                context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
                
                // 返回响应
                return complete(StatusCodes.OK, createSuccessResponse("POST request processed"));
            })
        );
    }

    /**
     * 处理PUT请求
     */
    private Route handlePutRequest() {
        return extractRequest(request -> 
            entity(Unmarshaller.entityToString(), body -> {
                log.debug("Received PUT request with body: {}", body);
                
                HTTPRequestMessage httpRequestMsg = createHttpRequestMessage(request, "PUT", body);
                context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
                
                return complete(StatusCodes.OK, createSuccessResponse("PUT request processed"));
            })
        );
    }

    /**
     * 处理DELETE请求
     */
    private Route handleDeleteRequest() {
        return extractRequest(request -> {
            log.debug("Received DELETE request: {}", request.getUri());
            
            HTTPRequestMessage httpRequestMsg = createHttpRequestMessage(request, "DELETE", "");
            context.getOutLet((short) 0x01).broadcast(httpRequestMsg);
            
            return complete(StatusCodes.OK, createSuccessResponse("DELETE request processed"));
        });
    }

    /**
     * 创建HTTP请求消息
     */
    private HTTPRequestMessage createHttpRequestMessage(HttpRequest request, String method, String body) {
        HTTPRequestMessage httpRequestMsg = new HTTPRequestMessage();
        httpRequestMsg.setMethod(HttpMethods.lookup(method).orElse(HttpMethods.GET));
        httpRequestMsg.setUri(request.getUri().toString());
        httpRequestMsg.setPayload(body);
        httpRequestMsg.setHeaders(extractHeaders(request));
        httpRequestMsg.setTimestamp(LocalDateTime.now());
        httpRequestMsg.setShapeInstanceId(getInstanceId());
        return httpRequestMsg;
    }

    /**
     * 提取HTTP头信息
     */
    private Map<String, String> extractHeaders(HttpRequest request) {
        Map<String, String> headers = new HashMap<>();
        request.getHeaders().forEach(header -> 
            headers.put(header.name(), header.value())
        );
        return headers;
    }

    /**
     * 创建成功响应
     */
    private String createSuccessResponse(String message) {
        return String.format("""
            {
              "status": "success",
              "message": "%s",
              "timestamp": "%s",
              "shapeId": "%s"
            }
            """, message, LocalDateTime.now(), getInstanceId());
    }

    /**
     * 创建状态响应
     */
    private String createStatusResponse() {
        return String.format("""
            {
              "shapeId": "%s",
              "status": "running",
              "middlewareId": "%s",
              "routePath": "%s",
              "timestamp": "%s"
            }
            """, getInstanceId(), options.getMiddlewareId(), 
            options.getRoutePath(), LocalDateTime.now());
    }

    /**
     * 发送状态事件
     */
    private void sendStateEvent(boolean isRunning, String message) {
        StateEventMessage stateEvent = new StateEventMessage();
        stateEvent.setPayload(isRunning);
        stateEvent.setMessage(message);
        stateEvent.setTimestamp(LocalDateTime.now());
        context.getOutLet((short) 0x02).broadcast(stateEvent);
    }

    /**
     * 获取HttpServerResource
     * 这里需要根据实际的架构实现
     */
    private Object getHttpServerResource(String middlewareId) {
        try {
            // 实际实现需要根据项目架构调整
            // 可能的实现方式：
            
            // 方式1: 通过Spring上下文获取ResourceRegistry
             ResourceRegistry resourceRegistry = SpringContextHolder.getBean(ResourceRegistry.class);
            
            // 方式2: 通过全局上下文获取
            // ResourceRegistry resourceRegistry = context.getGlobalContext().getResourceRegistry();
            
            // 方式3: 通过静态方法获取
            // ResourceRegistry resourceRegistry = ResourceRegistryHolder.getInstance();
            
            // 临时模拟实现
            log.warn("HttpServerResource access not implemented, using mock");
            return new Object(); // 返回模拟对象
            
        } catch (Exception e) {
            log.error("Failed to get HttpServerResource: {}", middlewareId, e);
            return null;
        }
    }

    /**
     * 绑定路由到服务器
     */
    private void bindRouteToServer(Route route) {
        // 实际实现需要调用HttpServerResource的bindRoute方法
        // httpServerResource.bindRoute(route);
        log.info("Route bound to server (mock implementation)");
    }

    @Override
    protected void processMessage(StreamMessage message) {
        // HTTP服务器Shape通常不处理输入消息
        // 它主要作为数据流的入口点
        log.debug("HTTP server shape received message: {}", message.getClass().getSimpleName());
    }

    /**
     * HTTP服务器Shape配置选项
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class HTTPServerShapeExampleOption extends StreamShapeOption {
        /**
         * HTTP SERVER 中间件的ID
         */
        private String middlewareId = "http-server-8080";
        
        /**
         * 自定义路由路径
         */
        private String routePath = "/api/stream";
        
        /**
         * 请求处理方式
         */
        private String handlerType = "ROUTE";
        
        /**
         * 响应超时时间（秒）
         */
        private int responseTimeout = 30;
        
        /**
         * 是否启用详细日志
         */
        private boolean verboseLogging = false;
    }

    /**
     * 状态事件消息
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class StateEventMessage extends EventMessage {
        private String message;
    }
}
