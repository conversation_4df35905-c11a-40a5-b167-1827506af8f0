package com.siteweb.tcs.backend.admin;

import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR> (2024-05-14)
 **/
@Component
public class AppInfoContributor implements InfoContributor {


    @Override
    public void contribute(Info.Builder builder) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("App Name", "Thing Collector Server");
        map.put("Version", "1.2.1");
        map.put("Build Time", "2024-05-14 14:32:22");
        builder.withDetails(map);
    }
}
