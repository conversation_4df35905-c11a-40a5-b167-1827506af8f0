package com.siteweb.tcs.backend.plugin;

import com.siteweb.tcs.common.runtime.ThingConnectPluginDescriptor;
import lombok.extern.slf4j.Slf4j;
import org.pf4j.PluginDescriptor;
import org.pf4j.PropertiesPluginDescriptorFinder;
import org.pf4j.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.Properties;

/**
 * 调试模式，直接读取项目的POM文件
 *
 * <AUTHOR> (2024-06-07)
 **/
@Slf4j
public class ThingConnectPropertiesPluginDescriptorFinder extends PropertiesPluginDescriptorFinder {

    public static final String PLUGIN_BUILD_TIME = "plugin.buildTime";
    public static final String PLUGIN_NAME_CLASS = "plugin.name";

    @Override
    public PluginDescriptor find(Path pluginPath) {
        Properties properties = readProperties(pluginPath);
        if (properties == null) return null;
        return createPluginDescriptor(properties);
    }


    protected Properties readProperties(Path pluginPath) {
        File file = pluginPath.getParent().resolve("pom.xml").toFile();
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = null;
        Properties properties = new Properties();
        try {
            builder = factory.newDocumentBuilder();
            Document document = builder.parse(file);
            document.getDocumentElement().normalize();
            NodeList propertiesNodeList = document.getElementsByTagName("properties");
            if (propertiesNodeList.getLength() > 0) {
                Element propertiesElement = (Element) propertiesNodeList.item(0);
                NodeList propertyNodes = propertiesElement.getChildNodes();
                for (int i = 0; i < propertyNodes.getLength(); i++) {
                    if (propertyNodes.item(i) instanceof Element propertyElement) {
                        String propertyName = propertyElement.getTagName();
                        String propertyValue = propertyElement.getTextContent().trim();
                        properties.setProperty(propertyName, propertyValue);
                    }
                }
            }
            return properties;
        } catch (ParserConfigurationException | SAXException | IOException e) {
            return null;
        }
    }


    @Override
    protected PluginDescriptor createPluginDescriptor(Properties properties) {
        ThingConnectPluginDescriptor pluginDescriptor = new ThingConnectPluginDescriptor();

        // TODO validate !!!
        String id = properties.getProperty(PLUGIN_ID);
        pluginDescriptor.setPluginId(id);

        String description = properties.getProperty(PLUGIN_DESCRIPTION);
        if (StringUtils.isNullOrEmpty(description)) {
            pluginDescriptor.setPluginDescription("");
        } else {
            pluginDescriptor.setPluginDescription(description);
        }

        String name = properties.getProperty(PLUGIN_NAME_CLASS);
        if (StringUtils.isNotNullOrEmpty(name)) {
            pluginDescriptor.setPluginName(name);
        }

        String clazz = properties.getProperty(PLUGIN_CLASS);
        if (StringUtils.isNotNullOrEmpty(clazz)) {
            pluginDescriptor.setPluginClass(clazz);
            pluginDescriptor.setPackageName(clazz.substring(0, clazz.lastIndexOf(".")));
        }

        String version = properties.getProperty(PLUGIN_VERSION);
        if (StringUtils.isNotNullOrEmpty(version)) {
            pluginDescriptor.setVersion(version);
        }

        String buildTime = properties.getProperty(PLUGIN_BUILD_TIME);
        if (StringUtils.isNotNullOrEmpty(buildTime)) {
            pluginDescriptor.setBuildTime(buildTime);
        }

        String provider = properties.getProperty(PLUGIN_PROVIDER);
        pluginDescriptor.setProvider(provider);

        String dependencies = properties.getProperty(PLUGIN_DEPENDENCIES);
        pluginDescriptor.setDependencies(dependencies);

        String requires = properties.getProperty(PLUGIN_REQUIRES);
        if (StringUtils.isNotNullOrEmpty(requires)) {
            pluginDescriptor.setRequires(requires);
        }

        pluginDescriptor.setLicense(properties.getProperty(PLUGIN_LICENSE));

        return pluginDescriptor;
    }


}
