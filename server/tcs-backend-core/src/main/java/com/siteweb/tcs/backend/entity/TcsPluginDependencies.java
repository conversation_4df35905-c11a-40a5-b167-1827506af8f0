package com.siteweb.tcs.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.tcs.backend.json.handlers.StringListHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@TableName(value = "tcs_plugin_dependencies", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class TcsPluginDependencies {

    /***
     * 应用名称
     */
    @TableField("applicationName")
    public String applicationName;


    /***
     * 插件ID
     */
    @TableField(value = "pluginId")
    private String pluginId;


    /***
     * 依赖关系列表
     */

    @TableField(value = "dependencies",typeHandler = StringListHandler.class)
    private List<String> dependencies;

}