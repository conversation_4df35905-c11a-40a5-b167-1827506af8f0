package com.siteweb.tcs.backend.controller;
import com.siteweb.tcs.backend.service.MonitorUnitService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/monitorUnit")
public class MonitorUnitController {

    @Autowired
    private MonitorUnitService monitorUnitService;

    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMonitorUnitList() {
        return ResponseHelper.successful(monitorUnitService.getMonitorUnitList());
    }

    @GetMapping(value = "/{monitorUnitID}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMonitorUnitByID(@PathVariable("monitorUnitID") Integer monitorUnitID) {
        return ResponseHelper.successful(monitorUnitService.getMonitorUnitByID(monitorUnitID));
    }

    //获取采集器数量
        @GetMapping(value = "/count", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCollectorCount() {
        return ResponseHelper.successful(monitorUnitService.getMonitorUnitCount());
    }

    //启动采集器
    @PostMapping(value = "/start/{monitorUnitID}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> startMonitorUnit(@PathVariable("monitorUnitID") Integer monitorUnitID) {
        monitorUnitService.control(monitorUnitID, LifeCycleEventType.START);
        return ResponseHelper.successful();
    }
    //停止采集器
    @PostMapping(value = "/stop/{monitorUnitID}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> stopMonitorUnit(@PathVariable("monitorUnitID") Integer monitorUnitID) {
        monitorUnitService.control(monitorUnitID, LifeCycleEventType.STOP);
        return ResponseHelper.successful();
    }

    //重启采集器
    @PostMapping(value = "/restart/{monitorUnitID}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> restartMonitorUnit(@PathVariable("monitorUnitID") Integer monitorUnitID) {
        monitorUnitService.control(monitorUnitID, LifeCycleEventType.RESTART);
        return ResponseHelper.successful();
    }


}
