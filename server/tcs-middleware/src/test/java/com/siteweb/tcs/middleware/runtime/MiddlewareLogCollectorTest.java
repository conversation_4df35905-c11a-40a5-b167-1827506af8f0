package com.siteweb.tcs.middleware.runtime;

import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollector;
import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollectorManager;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 中间件日志收集器测试
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@SpringBootTest
@ActiveProfiles("test")
public class MiddlewareLogCollectorTest {

    @Test
    public void testResourceLogCollectorCreation() {
        MiddlewareLogCollectorManager manager = new MiddlewareLogCollectorManager();
        
        String resourceId = "test-resource-001";
        MiddlewareLogCollector collector = manager.createResourceLogCollector(resourceId);
        
        assertNotNull(collector);
        assertEquals(resourceId, collector.getComponentId());
        assertEquals("resource", collector.getComponentType());
        
        // 测试日志流获取
        SseEmitter emitter = manager.getResourceLogStream(resourceId);
        assertNotNull(emitter);
        
        // 测试生命周期日志记录
        manager.logResourceLifecycle(resourceId, "INFO", "测试资源生命周期日志");
        
        // 清理
        manager.removeResourceLogCollector(resourceId);
        
        // 验证清理后无法获取日志流
        SseEmitter emitterAfterRemoval = manager.getResourceLogStream(resourceId);
        assertNull(emitterAfterRemoval);
    }

    @Test
    public void testServiceLogCollectorCreation() {
        MiddlewareLogCollectorManager manager = new MiddlewareLogCollectorManager();
        
        String serviceId = "test-service-001";
        MiddlewareLogCollector collector = manager.createServiceLogCollector(serviceId);
        
        assertNotNull(collector);
        assertEquals(serviceId, collector.getComponentId());
        assertEquals("service", collector.getComponentType());
        
        // 测试日志流获取
        SseEmitter emitter = manager.getServiceLogStream(serviceId);
        assertNotNull(emitter);
        
        // 测试生命周期日志记录
        manager.logServiceLifecycle(serviceId, "INFO", "测试服务生命周期日志");
        
        // 清理
        manager.removeServiceLogCollector(serviceId);
        
        // 验证清理后无法获取日志流
        SseEmitter emitterAfterRemoval = manager.getServiceLogStream(serviceId);
        assertNull(emitterAfterRemoval);
    }

    @Test
    public void testLogCollectorLifecycle() {
        MiddlewareLogCollector collector = new MiddlewareLogCollector("test-component", "test", "com.siteweb.tcs.middleware");
        
        // 测试手动添加生命周期日志
        collector.addLifecycleLog("INFO", "组件初始化");
        collector.addLifecycleLog("WARN", "组件警告");
        collector.addLifecycleLog("ERROR", "组件错误");
        
        // 验证日志数量
        MiddlewareLogCollector.LogInfo[] logs = collector.getLogs();
        assertTrue(logs.length >= 3);
        
        // 验证日志内容
        boolean foundInit = false, foundWarn = false, foundError = false;
        for (MiddlewareLogCollector.LogInfo log : logs) {
            if (log.getMessage().contains("组件初始化")) foundInit = true;
            if (log.getMessage().contains("组件警告")) foundWarn = true;
            if (log.getMessage().contains("组件错误")) foundError = true;
        }
        
        assertTrue(foundInit);
        assertTrue(foundWarn);
        assertTrue(foundError);
    }
}
