package com.siteweb.tcs.middleware.runtime;

import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollector;
import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollectorManager;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 中间件日志收集器测试
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@SpringBootTest
@ActiveProfiles("test")
public class MiddlewareLogCollectorTest {

    @Test
    public void testResourceLogCollectorCreation() {
        MiddlewareLogCollectorManager manager = new MiddlewareLogCollectorManager();
        
        String resourceId = "test-resource-001";
        MiddlewareLogCollector collector = manager.createResourceLogCollector(resourceId);
        
        assertNotNull(collector);
        assertEquals(resourceId, collector.getComponentId());
        assertEquals("resource", collector.getComponentType());
        
        // 测试日志流获取
        SseEmitter emitter = manager.getResourceLogStream(resourceId);
        assertNotNull(emitter);
        
        // 测试生命周期日志记录
        manager.logResourceLifecycle(resourceId, "INFO", "测试资源生命周期日志");
        
        // 清理
        manager.removeResourceLogCollector(resourceId);
        
        // 验证清理后无法获取日志流
        SseEmitter emitterAfterRemoval = manager.getResourceLogStream(resourceId);
        assertNull(emitterAfterRemoval);
    }

    @Test
    public void testServiceLogCollectorCreation() {
        MiddlewareLogCollectorManager manager = new MiddlewareLogCollectorManager();
        
        String serviceId = "test-service-001";
        MiddlewareLogCollector collector = manager.createServiceLogCollector(serviceId);
        
        assertNotNull(collector);
        assertEquals(serviceId, collector.getComponentId());
        assertEquals("service", collector.getComponentType());
        
        // 测试日志流获取
        SseEmitter emitter = manager.getServiceLogStream(serviceId);
        assertNotNull(emitter);
        
        // 测试生命周期日志记录
        manager.logServiceLifecycle(serviceId, "INFO", "测试服务生命周期日志");
        
        // 清理
        manager.removeServiceLogCollector(serviceId);
        
        // 验证清理后无法获取日志流
        SseEmitter emitterAfterRemoval = manager.getServiceLogStream(serviceId);
        assertNull(emitterAfterRemoval);
    }

    @Test
    public void testLogCollectorLifecycle() {
        MiddlewareLogCollector collector = new MiddlewareLogCollector("test-component", "test", "com.siteweb.tcs.middleware");

        // 测试手动添加生命周期日志
        collector.addLifecycleLog("INFO", "组件初始化");
        collector.addLifecycleLog("WARN", "组件警告");
        collector.addLifecycleLog("ERROR", "组件错误");

        // 验证日志数量
        MiddlewareLogCollector.LogInfo[] logs = collector.getLogs();
        assertTrue(logs.length >= 3);

        // 验证日志内容
        boolean foundInit = false, foundWarn = false, foundError = false;
        for (MiddlewareLogCollector.LogInfo log : logs) {
            if (log.getMessage().contains("组件初始化")) foundInit = true;
            if (log.getMessage().contains("组件警告")) foundWarn = true;
            if (log.getMessage().contains("组件错误")) foundError = true;
        }

        assertTrue(foundInit);
        assertTrue(foundWarn);
        assertTrue(foundError);
    }

    @Test
    public void testRecentLogsRetrieval() {
        MiddlewareLogCollectorManager manager = new MiddlewareLogCollectorManager();

        String resourceId = "test-resource-recent";
        MiddlewareLogCollector collector = manager.createResourceLogCollector(resourceId);

        // 添加多条日志
        for (int i = 1; i <= 150; i++) {
            collector.addLifecycleLog("INFO", "测试日志 " + i);
        }

        // 测试获取最近100条日志
        var recentLogs = manager.getResourceRecentLogs(resourceId, 100);
        assertEquals(100, recentLogs.length);

        // 验证返回的是最新的100条日志
        assertTrue(recentLogs[0].getMessage().contains("测试日志 51"));
        assertTrue(recentLogs[99].getMessage().contains("测试日志 150"));

        // 测试获取少于总数的日志
        var fewLogs = manager.getResourceRecentLogs(resourceId, 10);
        assertEquals(10, fewLogs.length);
        assertTrue(fewLogs[9].getMessage().contains("测试日志 150"));

        // 清理
        manager.removeResourceLogCollector(resourceId);
    }

    @Test
    public void testServiceRecentLogs() {
        MiddlewareLogCollectorManager manager = new MiddlewareLogCollectorManager();

        String serviceId = "test-service-recent";
        MiddlewareLogCollector collector = manager.createServiceLogCollector(serviceId);

        // 添加一些日志
        for (int i = 1; i <= 50; i++) {
            collector.addLifecycleLog("INFO", "服务日志 " + i);
        }

        // 测试获取最近100条日志（实际只有50条）
        var recentLogs = manager.getServiceRecentLogs(serviceId, 100);
        assertEquals(50, recentLogs.length);

        // 验证日志内容
        assertTrue(recentLogs[0].getMessage().contains("服务日志 1"));
        assertTrue(recentLogs[49].getMessage().contains("服务日志 50"));

        // 清理
        manager.removeServiceLogCollector(serviceId);
    }
}
