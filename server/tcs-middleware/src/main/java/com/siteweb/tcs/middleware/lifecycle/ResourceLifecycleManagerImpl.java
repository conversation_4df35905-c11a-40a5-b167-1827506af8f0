package com.siteweb.tcs.middleware.lifecycle;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.lifecycle.IResourceInitializer;
import com.siteweb.tcs.middleware.common.lifecycle.ResourceLifecycleManager;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceProvider;
import com.siteweb.tcs.middleware.common.resource.ResourceProviderFactory;
import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollectorManager;
import com.siteweb.tcs.middleware.entity.ResourceConfigurationEntity;
import com.siteweb.tcs.middleware.entity.ResourceTypeEntity;
import com.siteweb.tcs.middleware.service.ResourceConfigurationService;
import com.siteweb.tcs.middleware.service.ResourceTypeService;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 默认资源生命周期管理器实现
 */
@Component
public class ResourceLifecycleManagerImpl implements ResourceLifecycleManager, IResourceInitializer {

    private static final Logger logger = LoggerFactory.getLogger(ResourceLifecycleManagerImpl.class);
    @Autowired
    @Lazy
    private ResourceRegistry resourceRegistry;

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    @Autowired
    private ResourceConfigurationService resourceConfigurationService;

    @Autowired
    private ResourceTypeService resourceTypeService;

    @Autowired
    private MiddlewareLogCollectorManager logCollectorManager;

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @PostConstruct
    public void init() {
        // 启动定期健康检查
        scheduler.scheduleAtFixedRate(this::performHealthCheck, 60, 300, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        scheduler.shutdown();
    }

    @Override
    public Resource createResource(String resourceId) throws MiddlewareTechnicalException {
        try {
            return initializeResourceById(resourceId);
        } catch (Exception e) {
            if (e instanceof MiddlewareTechnicalException) {
                throw (MiddlewareTechnicalException) e;
            }
            if (e instanceof MiddlewareBusinessException) {
                throw (MiddlewareBusinessException) e;
            }
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.LIFECYCLE_OPERATION_FAILED,
                "Failed to initialize resource: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public Resource initializeResourceById(String resourceId) throws Exception {
        logger.debug("Creating resource: {}", resourceId);

        // 创建日志收集器
        logCollectorManager.createResourceLogCollector(resourceId);
        logCollectorManager.logResourceLifecycle(resourceId, "INFO", "开始初始化资源: " + resourceId);

        try {
            // 从数据库获取资源配置
            ResourceConfigurationEntity resourceConfigEntity = resourceConfigurationService.getResourceConfigurationById(resourceId);
            if (resourceConfigEntity == null) {
                logCollectorManager.logResourceLifecycle(resourceId, "ERROR", "资源配置未找到: " + resourceId);
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_NOT_FOUND,
                    "Resource configuration not found: " + resourceId
                );
            }

            // 检查资源配置状态
            if (!"ENABLED".equals(resourceConfigEntity.getStatus())) {
                logCollectorManager.logResourceLifecycle(resourceId, "ERROR", "资源配置已禁用: " + resourceId + ", 状态: " + resourceConfigEntity.getStatus());
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_DISABLED,
                    "Resource configuration is disabled: " + resourceId + ", status: " + resourceConfigEntity.getStatus()
                );
            }

            // 获取资源类型
            ResourceTypeEntity resourceTypeEntity = resourceTypeService.getResourceTypeById(resourceConfigEntity.getResourceId());
            if (resourceTypeEntity == null) {
                logCollectorManager.logResourceLifecycle(resourceId, "ERROR", "资源类型未找到: " + resourceConfigEntity.getResourceId());
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_TYPE_INVALID,
                    "Resource type not found: " + resourceConfigEntity.getResourceId()
                );
            }

            String resourceType = resourceTypeEntity.getId();
            String resourceName = resourceConfigEntity.getName();
            String resourceDescription = resourceConfigEntity.getDescription();
            Map<String, Object> config = resourceConfigEntity.getConfig();

            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "获取资源提供者: " + resourceType);

            // 获取资源提供者
            ResourceProvider<? extends Resource> provider = resourceProviderFactory.getProvider(resourceType);

            // 验证资源配置
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "验证资源配置");
            provider.validateConfig(config);

            // 创建资源实例
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "创建资源实例");
            Resource resource = provider.createResource(resourceId, resourceName, resourceDescription, config);

            // 初始化资源
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "初始化资源");
            resource.initialize();

            // 启动资源
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "启动资源");
            resource.start();

            // 保存到注册表
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "保存资源到注册表");
            resourceRegistry.save(resource);

            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "资源初始化完成: " + resourceId);
            return resource;
        } catch (Exception e) {
            logCollectorManager.logResourceLifecycle(resourceId, "ERROR", "资源初始化失败: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public HealthStatus checkResourceHealth(Resource resource) {
        return resource.checkHealth();
    }

    @Override
    public void destroyResource(Resource resource) throws MiddlewareTechnicalException {
        String resourceId = resource.getId();
        logger.debug("Destroying resource: {}", resourceId);

        logCollectorManager.logResourceLifecycle(resourceId, "INFO", "开始销毁资源: " + resourceId);

        try {
            // 停止资源
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "停止资源");
            resource.stop();

            // 销毁资源
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "销毁资源");
            resource.destroy();

            // 从注册表中移除
            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "从注册表中移除资源");
            resourceRegistry.remove(resourceId);

            logCollectorManager.logResourceLifecycle(resourceId, "INFO", "资源销毁完成: " + resourceId);

            // 移除日志收集器
            logCollectorManager.removeResourceLogCollector(resourceId);

            logger.debug("Resource destroyed successfully: {}", resourceId);
        } catch (Exception e) {
            logCollectorManager.logResourceLifecycle(resourceId, "ERROR", "资源销毁失败: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public void performHealthCheck() {
        logger.debug("Performing health check for all resources");

        resourceRegistry.getAll().forEach(resource -> {
            try {
                HealthStatus healthStatus = resource.checkHealth();
                if (!healthStatus.isUp()) {
                    logger.warn("Resource {} is not healthy: {}", resource.getId(), healthStatus.getMessage());
                    // 可以在这里添加资源恢复逻辑
                }
            } catch (Exception e) {
                logger.error("Failed to check health for resource {}: {}", resource.getId(), e.getMessage(), e);
            }
        });
    }
}
