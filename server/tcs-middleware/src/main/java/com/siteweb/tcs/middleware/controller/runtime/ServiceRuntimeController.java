package com.siteweb.tcs.middleware.controller.runtime;

import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollectorManager;
import com.siteweb.tcs.middleware.dto.ServiceInstantiationResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.service.ServiceRuntimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 服务运行时控制器
 * 提供服务的运行时操作接口
 */
@RestController
@RequestMapping("/middleware/service-runtime")
@Tag(name = "服务运行时管理", description = "服务运行时操作接口")
public class ServiceRuntimeController {

    @Autowired
    private ServiceRuntimeService serviceRuntimeService;

    @Autowired
    private MiddlewareLogCollectorManager logCollectorManager;

    /**
     * 测试服务连接
     *
     * @param serviceTypeId 服务类型ID
     * @param config 服务配置
     * @return 连接测试结果
     */
    @PostMapping("/test-connection")
    @Operation(summary = "测试服务连接", description = "测试指定服务类型和配置的连接")
    public ResponseResult<ConnectionTestResult> testConnection(
            @Parameter(description = "服务类型ID", required = true) @RequestParam String serviceTypeId,
            @Parameter(description = "服务配置", required = true) @RequestBody Map<String, Object> config) {

        ConnectionTestResult result = serviceRuntimeService.testConnection(serviceTypeId, config);
        return ResponseResult.success(result);
    }

    /**
     * 验证服务配置
     *
     * @param serviceTypeId 服务类型ID
     * @param config 服务配置
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    @Operation(summary = "验证服务配置", description = "验证指定服务类型的配置是否有效")
    public ResponseResult<ValidationResult> validateConfig(
            @Parameter(description = "服务类型ID", required = true) @RequestParam String serviceTypeId,
            @Parameter(description = "服务配置", required = true) @RequestBody Map<String, Object> config) {

        ValidationResult result = serviceRuntimeService.validateConfig(serviceTypeId, config);
        return ResponseResult.success(result);
    }

    /**
     * 检查服务是否已实例化
     *
     * @param serviceConfigId 服务配置ID
     * @return 是否已实例化
     */
    @GetMapping("/is-instantiated/{serviceConfigId}")
    @Operation(summary = "检查服务是否已实例化", description = "检查指定服务配置是否已实例化")
    public ResponseResult<Boolean> isInstantiated(@PathVariable String serviceConfigId) {
        boolean instantiated = serviceRuntimeService.isServiceInstantiated(serviceConfigId);
        return ResponseResult.success(instantiated);
    }

    /**
     * 实例化服务
     *
     * @param serviceConfigId 服务配置ID
     * @return 实例化结果
     */
    @PostMapping("/instantiate/{serviceConfigId}")
    @Operation(summary = "实例化服务", description = "实例化指定服务配置")
    public ResponseResult<ServiceInstantiationResult> instantiate(@PathVariable String serviceConfigId) {
        ServiceInstantiationResult result = serviceRuntimeService.instantiateService(serviceConfigId);
        return ResponseResult.success(result);
    }

    /**
     * 销毁服务实例
     *
     * @param serviceConfigId 服务配置ID
     * @return 销毁结果
     */
    @PostMapping("/destroy/{serviceConfigId}")
    @Operation(summary = "销毁服务实例", description = "销毁指定服务配置的实例")
    public ResponseResult<Boolean> destroy(@PathVariable String serviceConfigId) {
        boolean success = serviceRuntimeService.destroyService(serviceConfigId);
        return ResponseResult.success(success);
    }

    /**
     * 检查服务健康状态
     *
     * @param serviceConfigId 服务配置ID
     * @return 健康状态
     */
    @GetMapping("/health/{serviceConfigId}")
    @Operation(summary = "检查服务健康状态", description = "检查指定服务配置的健康状态")
    public ResponseResult<HealthStatus> checkHealth(@PathVariable String serviceConfigId) {
        HealthStatus healthStatus = serviceRuntimeService.checkServiceHealth(serviceConfigId);
        return ResponseResult.success(healthStatus);
    }

    /**
     * 获取服务日志流
     *
     * @param serviceId 服务ID
     * @return SSE日志流
     */
    @GetMapping("logs-stream/{serviceId}")
    @Operation(summary = "获取服务日志流", description = "获取指定服务的实时日志流")
    public ResponseEntity<SseEmitter> streamLogs(@PathVariable String serviceId) {
        SseEmitter emitter = logCollectorManager.getServiceLogStream(serviceId);
        if (emitter == null) {
            return new ResponseEntity<SseEmitter>((SseEmitter) null, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<SseEmitter>(emitter, HttpStatus.OK);
    }

}
