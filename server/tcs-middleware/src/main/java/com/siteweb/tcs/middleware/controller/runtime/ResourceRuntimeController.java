package com.siteweb.tcs.middleware.controller.runtime;

import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollectorManager;
import com.siteweb.tcs.middleware.dto.ResourceInstantiationResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.service.ResourceRuntimeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;

/**
 * 资源运行时控制器
 * 提供资源的运行时操作接口
 */
@RestController
@RequestMapping("/middleware/resource-runtime")
@Tag(name = "资源运行时管理", description = "资源运行时操作接口")
public class ResourceRuntimeController {

    @Autowired
    private ResourceRuntimeService resourceRuntimeService;

    @Autowired
    private MiddlewareLogCollectorManager logCollectorManager;

    /**
     * 测试资源连接
     *
     * @param resourceTypeId 资源类型ID
     * @param config 资源配置
     * @return 连接测试结果
     */
    @PostMapping("/test-connection")
    @Operation(summary = "测试资源连接", description = "测试指定资源类型和配置的连接")
    public ResponseResult<ConnectionTestResult> testConnection(
            @Parameter(description = "资源类型ID", required = true) @RequestParam String resourceTypeId,
            @Parameter(description = "资源配置", required = true) @RequestBody Map<String, Object> config) {

        ConnectionTestResult result = resourceRuntimeService.testConnection(resourceTypeId, config);
        return ResponseResult.success(result);
    }

    /**
     * 验证资源配置
     *
     * @param resourceTypeId 资源类型ID
     * @param config 资源配置
     * @return 验证结果
     */
    @PostMapping("/validate-config")
    @Operation(summary = "验证资源配置", description = "验证指定资源类型的配置是否有效")
    public ResponseResult<ValidationResult> validateConfig(
            @Parameter(description = "资源类型ID", required = true) @RequestParam String resourceTypeId,
            @Parameter(description = "资源配置", required = true) @RequestBody Map<String, Object> config) {

        ValidationResult result = resourceRuntimeService.validateConfig(resourceTypeId, config);
        return ResponseResult.success(result);
    }

    /**
     * 检查资源是否已实例化
     *
     * @param resourceConfigId 资源配置ID
     * @return 是否已实例化
     */
    @GetMapping("/is-instantiated/{resourceConfigId}")
    @Operation(summary = "检查资源是否已实例化", description = "检查指定资源配置是否已实例化")
    public ResponseResult<Boolean> isInstantiated(@PathVariable String resourceConfigId) {
        boolean instantiated = resourceRuntimeService.isResourceInstantiated(resourceConfigId);
        return ResponseResult.success(instantiated);
    }

    /**
     * 实例化资源
     *
     * @param resourceConfigId 资源配置ID
     * @return 实例化结果
     */
    @PostMapping("/instantiate/{resourceConfigId}")
    @Operation(summary = "实例化资源", description = "实例化指定资源配置")
    public ResponseResult<ResourceInstantiationResult> instantiate(@PathVariable String resourceConfigId) {
        ResourceInstantiationResult result = resourceRuntimeService.instantiateResource(resourceConfigId);
        return ResponseResult.success(result);
    }

    /**
     * 销毁资源实例
     *
     * @param resourceConfigId 资源配置ID
     * @return 销毁结果
     */
    @PostMapping("/destroy/{resourceConfigId}")
    @Operation(summary = "销毁资源实例", description = "销毁指定资源配置的实例")
    public ResponseResult<Boolean> destroy(@PathVariable String resourceConfigId) {
        boolean success = resourceRuntimeService.destroyResource(resourceConfigId);
        return ResponseResult.success(success);
    }

    /**
     * 检查资源健康状态
     *
     * @param resourceConfigId 资源配置ID
     * @return 健康状态
     */
    @GetMapping("/health/{resourceConfigId}")
    @Operation(summary = "检查资源健康状态", description = "检查指定资源配置的健康状态")
    public ResponseResult<HealthStatus> checkHealth(@PathVariable String resourceConfigId) {
        HealthStatus healthStatus = resourceRuntimeService.checkResourceHealth(resourceConfigId);
        return ResponseResult.success(healthStatus);
    }

    /**
     * 获取资源日志流
     *
     * @param resourceId 资源ID
     * @return SSE日志流
     */
    @GetMapping("logs-stream")
    @Operation(summary = "获取资源日志流", description = "获取指定资源的实时日志流")
    public ResponseEntity<SseEmitter> streamLogs(@RequestParam() String resourceId) {
        SseEmitter emitter = logCollectorManager.getResourceLogStream(resourceId);
        if (emitter == null) {
            return new ResponseEntity<SseEmitter>((SseEmitter) null, HttpStatus.BAD_REQUEST);
        }
        return new ResponseEntity<SseEmitter>(emitter, HttpStatus.OK);
    }

    /**
     * 获取资源最近日志
     *
     * @param resourceId 资源ID
     * @return 最近100条日志
     */
    @GetMapping("logs/{resourceId}")
    @Operation(summary = "获取资源最近日志", description = "获取指定资源的最近100条日志")
    public ResponseResult<Object> getLogs(@PathVariable String resourceId) {
        var logs = logCollectorManager.getResourceRecentLogs(resourceId, 100);
        if (logs.length == 0) {
            var collector = logCollectorManager.getResourceLogCollector(resourceId);
            if (collector == null) {
                return ResponseResult.fail("Resource log collector not found: " + resourceId);
            }
        }
        return ResponseResult.success(logs);
    }

}
