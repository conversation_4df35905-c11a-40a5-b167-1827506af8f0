package com.siteweb.tcs.middleware.factory;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import com.siteweb.tcs.middleware.common.service.provider.DatabaseServiceProvider;
import com.siteweb.tcs.middleware.common.service.provider.KeyValueStoreServiceProvider;
import com.siteweb.tcs.middleware.common.service.provider.SitewebPersistentServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceProviderFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 服务提供者工厂实现类
 * 使用Spring容器管理服务提供者实例，确保依赖注入正常工作
 */
@Component
public class ServiceProviderFactoryImpl implements ServiceProviderFactory {

    private static final Logger logger = LoggerFactory.getLogger(ServiceProviderFactoryImpl.class);

    @Autowired
    private ApplicationContext applicationContext;

    // 使用实例Map存储服务提供者实例，避免重复创建
    private final Map<String, ServiceProvider<? extends Service>> providerMap = new ConcurrentHashMap<>();

    /**
     * 获取服务提供者
     *
     * @param type 服务类型
     * @return 服务提供者
     */
    private ServiceProvider<? extends Service> getProviderByType(String type) {
        // 使用computeIfAbsent方法，如果不存在则创建新的提供者
        return providerMap.computeIfAbsent(type, key -> {
            // 根据类型创建新的提供者
            ServiceProvider<? extends Service> provider = createProvider(key);

            // 如果创建成功，则记录日志
            if (provider != null) {
                logger.info("创建服务提供者: {}", key);
            }

            return provider;
        });
    }

    /**
     * 根据类型创建服务提供者
     *
     * @param type 服务类型
     * @return 服务提供者
     */
    private ServiceProvider<? extends Service> createProvider(String type) {
        // 使用ServiceType枚举的fromCodeIgnoreCase方法，忽略大小写
        ServiceType serviceType = ServiceType.fromCodeIgnoreCase(type);

        if (serviceType == null) {
            return null;
        }

        try {
            // 使用Spring容器获取Provider实例，确保依赖注入正常工作
            switch (serviceType) {
                case DATABASE:
                    return applicationContext.getBean(DatabaseServiceProvider.class);
                case KEY_VALUE_STORE:
                    return applicationContext.getBean(KeyValueStoreServiceProvider.class);
                case SITEWEB_PERSISTENT:
                    return applicationContext.getBean(SitewebPersistentServiceProvider.class);
                // 可以在这里添加更多服务类型的case
                default:
                    return null;
            }
        } catch (Exception e) {
            logger.error("Failed to create service provider for type: {}", type, e);
            return null;
        }
    }

    @Override
    public ServiceProvider<? extends Service> getProvider(String type) throws MiddlewareBusinessException {
        ServiceProvider<? extends Service> provider = getProviderByType(type);
        if (provider == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.SERVICE_TYPE_INVALID,
                "服务类型不存在: " + type
            );
        }
        return provider;
    }

    @Override
    public Map<String, ServiceProvider<? extends Service>> getAllProviders() {
        // 这里我们需要确保所有已知的服务提供者类型都被加载
        // 这样getAllProviders才能返回所有可能的提供者
        getProviderByType(ServiceType.DATABASE.getCode());
        getProviderByType(ServiceType.KEY_VALUE_STORE.getCode());
        getProviderByType(ServiceType.SITEWEB_PERSISTENT.getCode());
        // 可以在这里添加更多服务类型

        return new HashMap<>(providerMap);
    }
}
