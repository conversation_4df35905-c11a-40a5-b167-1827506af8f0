package com.siteweb.tcs.middleware.lifecycle;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.lifecycle.IServiceInitializer;
import com.siteweb.tcs.middleware.common.lifecycle.ResourceLifecycleManager;
import com.siteweb.tcs.middleware.common.lifecycle.ServiceLifecycleManager;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.runtime.MiddlewareLogCollectorManager;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceProviderFactory;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.middleware.entity.ServiceConfigurationEntity;
import com.siteweb.tcs.middleware.entity.ServiceTypeEntity;
import com.siteweb.tcs.middleware.service.ServiceConfigurationService;
import com.siteweb.tcs.middleware.service.ServiceTypeService;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 默认服务生命周期管理器实现
 */
@Component
public class ServiceLifecycleManagerImpl implements ServiceLifecycleManager, IServiceInitializer {

    private static final Logger logger = LoggerFactory.getLogger(ServiceLifecycleManagerImpl.class);

    private ServiceRegistry serviceRegistry;

    private ResourceRegistry resourceRegistry;

    private ServiceProviderFactory serviceProviderFactory;

    private ResourceLifecycleManager resourceLifecycleManager;

    private ServiceConfigurationService serviceConfigurationService;

    private ServiceTypeService serviceTypeService;

    @Autowired
    public void setServiceRegistry(@Lazy ServiceRegistry serviceRegistry) {
        this.serviceRegistry = serviceRegistry;
    }

    @Autowired
    public void setResourceRegistry(@Lazy ResourceRegistry resourceRegistry) {
        this.resourceRegistry = resourceRegistry;
    }

    @Autowired
    public void setServiceProviderFactory(ServiceProviderFactory serviceProviderFactory) {
        this.serviceProviderFactory = serviceProviderFactory;
    }

    @Autowired
    public void setResourceLifecycleManager(ResourceLifecycleManager resourceLifecycleManager) {
        this.resourceLifecycleManager = resourceLifecycleManager;
    }

    @Autowired
    public void setServiceConfigurationService(ServiceConfigurationService serviceConfigurationService) {
        this.serviceConfigurationService = serviceConfigurationService;
    }

    @Autowired
    public void setServiceTypeService(ServiceTypeService serviceTypeService) {
        this.serviceTypeService = serviceTypeService;
    }

    private MiddlewareLogCollectorManager logCollectorManager;

    @Autowired
    public void setLogCollectorManager(MiddlewareLogCollectorManager logCollectorManager) {
        this.logCollectorManager = logCollectorManager;
    }

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @PostConstruct
    public void init() {
        // 启动定期健康检查
        scheduler.scheduleAtFixedRate(this::performHealthCheck, 90, 300, TimeUnit.SECONDS);
    }

    @PreDestroy
    public void destroy() {
        scheduler.shutdown();
    }

    @Override
    public Service createService(String serviceId) throws MiddlewareTechnicalException {
        try {
            return initializeServiceById(serviceId);
        } catch (Exception e) {
            if (e instanceof MiddlewareTechnicalException) {
                throw (MiddlewareTechnicalException) e;
            }
            if (e instanceof MiddlewareBusinessException) {
                throw (MiddlewareBusinessException) e;
            }
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.LIFECYCLE_OPERATION_FAILED,
                "Failed to initialize service: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public Service initializeServiceById(String serviceId) throws Exception {
        logger.debug("Creating service: {}", serviceId);

        // 创建日志收集器
        logCollectorManager.createServiceLogCollector(serviceId);
        logCollectorManager.logServiceLifecycle(serviceId, "INFO", "开始初始化服务: " + serviceId);

        try {
            // 从数据库获取服务配置
            ServiceConfigurationEntity serviceConfigEntity = serviceConfigurationService.getServiceConfigurationById(serviceId);
            if (serviceConfigEntity == null) {
                logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "服务配置未找到: " + serviceId);
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_NOT_FOUND,
                    "Service configuration not found: " + serviceId
                );
            }

            // 检查服务配置状态
            if (!"ENABLED".equals(serviceConfigEntity.getStatus())) {
                logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "服务配置已禁用: " + serviceId + ", 状态: " + serviceConfigEntity.getStatus());
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_DISABLED,
                    "Service configuration is disabled: " + serviceId + ", status: " + serviceConfigEntity.getStatus()
                );
            }

            // 获取服务类型
            ServiceTypeEntity serviceTypeEntity = serviceTypeService.getServiceTypeById(serviceConfigEntity.getServiceId());
            if (serviceTypeEntity == null) {
                logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "服务类型未找到: " + serviceConfigEntity.getServiceId());
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_TYPE_INVALID,
                    "Service type not found: " + serviceConfigEntity.getServiceId()
                );
            }

            String serviceType = serviceTypeEntity.getId();
            String serviceName = serviceConfigEntity.getName();
            String serviceDescription = serviceConfigEntity.getDescription();
            String resourceId = serviceConfigEntity.getResourceConfigurationId();
            String supportedResourceCategory = serviceTypeEntity.getSupportedResourceCategory();
            Map<String, Object> config = serviceConfigEntity.getConfig();

            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "服务类型: " + serviceType + ", 支持的资源类别: " + supportedResourceCategory);

            // 处理资源依赖
            Resource resource = null;

            // 根据服务类型的supportedResourceCategory判断是否需要资源
            if ("NONE".equals(supportedResourceCategory)) {
                // 服务类型不需要资源（如SitewebPersistentService）
                logCollectorManager.logServiceLifecycle(serviceId, "INFO", "服务不需要资源依赖");
                logger.debug("Service {} of type {} does not require a resource (supportedResourceCategory=NONE)",
                            serviceId, serviceType);
            } else {
                // 服务类型需要资源，检查是否提供了资源配置
                if (resourceId == null || resourceId.trim().isEmpty()) {
                    logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "服务需要资源但未提供资源配置");
                    throw new MiddlewareBusinessException(
                        MiddlewareBusinessErrorCode.RESOURCE_DEPENDENCY_MISSING,
                        String.format("Service type %s requires a resource (supportedResourceCategory=%s), but no resource configuration provided",
                                    serviceType, supportedResourceCategory)
                    );
                }

                logCollectorManager.logServiceLifecycle(serviceId, "INFO", "获取关联资源: " + resourceId);

                // 从注册表中获取关联的资源
                // 注册表会自动处理资源的创建，避免重复创建
                // 这里会调用ResourceRegistry.get()，进而调用ResourceLifecycleManagerImpl.initializeResourceById()
                // 在那里会检查资源配置的状态，如果资源被禁用会抛出RESOURCE_DISABLED异常
                resource = resourceRegistry.get(resourceId);

                if (resource == null) {
                    logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "获取或创建资源失败: " + resourceId);
                    throw new MiddlewareBusinessException(
                        MiddlewareBusinessErrorCode.RESOURCE_DEPENDENCY_MISSING,
                        "Failed to get or create resource: " + resourceId
                    );
                }
                logCollectorManager.logServiceLifecycle(serviceId, "INFO", "成功获取资源: " + resourceId);
                logger.debug("Service {} successfully obtained resource: {} for category: {}",
                            serviceId, resourceId, supportedResourceCategory);
            }

            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "获取服务提供者: " + serviceType);

            // 获取服务提供者
            ServiceProvider<? extends Service> provider = serviceProviderFactory.getProvider(serviceType);

            // 验证服务配置
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "验证服务配置");
            provider.validateConfig(config);

            // 创建服务实例
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "创建服务实例");
            Service service = provider.createService(serviceId, serviceName, serviceDescription, config, resource);

            // 初始化服务
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "初始化服务");
            service.initialize();

            // 启动服务
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "启动服务");
            service.start();

            // 保存到注册表
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "保存服务到注册表");
            serviceRegistry.save(service);

            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "服务初始化完成: " + serviceId);
            return service;
        } catch (Exception e) {
            logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "服务初始化失败: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public HealthStatus checkServiceHealth(Service service) {
        return service.checkHealth();
    }

    @Override
    public void destroyService(Service service) throws MiddlewareTechnicalException {
        String serviceId = service.getId();
        logger.debug("Destroying service: {}", serviceId);

        logCollectorManager.logServiceLifecycle(serviceId, "INFO", "开始销毁服务: " + serviceId);

        try {
            // 停止服务
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "停止服务");
            service.stop();

            // 销毁服务
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "销毁服务");
            service.destroy();

            // 从注册表中移除
            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "从注册表中移除服务");
            serviceRegistry.remove(serviceId);

            logCollectorManager.logServiceLifecycle(serviceId, "INFO", "服务销毁完成: " + serviceId);

            // 移除日志收集器
            logCollectorManager.removeServiceLogCollector(serviceId);

            logger.debug("Service destroyed successfully: {}", serviceId);
        } catch (Exception e) {
            logCollectorManager.logServiceLifecycle(serviceId, "ERROR", "服务销毁失败: " + e.getMessage());
            throw e;
        }
    }

    @Override
    public void performHealthCheck() {
        logger.debug("Performing health check for all services");

        serviceRegistry.getAll().forEach(service -> {
            try {
                HealthStatus healthStatus = service.checkHealth();
                if (!healthStatus.isUp()) {
                    logger.warn("Service {} is not healthy: {}", service.getId(), healthStatus.getMessage());
                    // 可以在这里添加服务恢复逻辑
                }
            } catch (Exception e) {
                logger.error("Failed to check health for service {}: {}", service.getId(), e.getMessage(), e);
            }
        });
    }

    @Override
    public SitewebPersistentService createDefaultSitewebPersistentService(String serviceId) throws MiddlewareTechnicalException {
        try {
            logger.debug("Creating default SitewebPersistentService with hardcoded config: {}", serviceId);

            // 硬编码的服务配置，不依赖数据库
            String serviceName = "默认Siteweb持久化服务";
            String serviceDescription = "用于访问tcs-siteweb模块服务的默认实例，使用硬编码配置";
            String serviceType = ServiceType.SITEWEB_PERSISTENT.getCode();
            Map<String, Object> config = new HashMap<>(); // 空配置

            // 获取服务提供者
            ServiceProvider<? extends Service> provider = serviceProviderFactory.getProvider(serviceType);
            if (provider == null) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_PROVIDER_NOT_FOUND,
                    "Service provider not found for type: " + serviceType
                );
            }

            // 验证服务配置（虽然是空配置，但仍需验证）
            provider.validateConfig(config);

            // 创建服务实例（SitewebPersistentService不需要Resource，传入null）
            Service service = provider.createService(serviceId, serviceName, serviceDescription, config, null);

            if (!(service instanceof SitewebPersistentService)) {
                throw new MiddlewareTechnicalException(
                    MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Expected SitewebPersistentService but got: " + service.getClass().getName()
                );
            }

            // 初始化服务
            service.initialize();

            // 启动服务
            service.start();

            // 保存到注册表
            serviceRegistry.save(service);

            logger.info("Successfully created default SitewebPersistentService: {}", serviceId);
            return (SitewebPersistentService) service;

        } catch (Exception e) {
            logger.error("Failed to create default SitewebPersistentService: {}", serviceId, e);
            if (e instanceof MiddlewareTechnicalException) {
                throw (MiddlewareTechnicalException) e;
            }
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to create default SitewebPersistentService: " + e.getMessage(),
                e
            );
        }
    }

}
