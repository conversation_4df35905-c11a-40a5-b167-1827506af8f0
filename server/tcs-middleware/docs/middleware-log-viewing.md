# 中间件日志查看功能

## 概述

本文档描述了为中间件模块的Resource和Service添加的日志查看功能，该功能类似于现有的插件日志查看系统，支持实时日志流和完整的生命周期日志记录。

## 功能特性

### 1. 实时日志流
- 基于Server-Sent Events (SSE) 的实时日志传输
- 支持按Resource ID或Service ID过滤日志
- 自动处理连接管理和错误恢复

### 2. 生命周期日志记录
- 记录Resource和Service的完整生命周期：
  - 初始化开始/完成
  - 配置验证
  - 资源创建
  - 启动/停止
  - 销毁
  - 错误处理

### 3. 日志级别支持
- 支持标准日志级别：INFO、WARN、ERROR、DEBUG
- 根据Logger配置自动过滤日志级别

## API接口

### Resource日志查看

#### 获取Resource日志流
```
GET /middleware/resource-runtime/logs-stream?resourceId={resourceId}
```

**参数：**
- `resourceId`: 资源配置ID

**响应：**
- Content-Type: `text/event-stream`
- 事件类型：
  - `connected`: 连接建立时发送历史日志
  - `append`: 新日志追加事件
  - `clean`: 清理事件

**示例：**
```bash
curl -N "http://localhost:8080/middleware/resource-runtime/logs-stream?resourceId=test-redis-001"
```

### Service日志查看

#### 获取Service日志流
```
GET /middleware/service-runtime/logs-stream?serviceId={serviceId}
```

**参数：**
- `serviceId`: 服务配置ID

**响应：**
- Content-Type: `text/event-stream`
- 事件格式同Resource日志流

**示例：**
```bash
curl -N "http://localhost:8080/middleware/service-runtime/logs-stream?serviceId=test-db-service-001"
```

## 日志格式

### 日志信息结构
```json
{
  "timeStamp": "2025-01-28T10:30:45.123",
  "level": "INFO",
  "loggerName": "middleware.resource",
  "message": "资源初始化完成: test-redis-001",
  "thread": "main",
  "componentId": "test-redis-001",
  "componentType": "resource"
}
```

### 字段说明
- `timeStamp`: 日志时间戳
- `level`: 日志级别
- `loggerName`: Logger名称
- `message`: 日志消息
- `thread`: 线程名称
- `componentId`: 组件ID（Resource ID或Service ID）
- `componentType`: 组件类型（"resource"或"service"）

## 生命周期日志示例

### Resource生命周期日志
```
2025-01-28 10:30:45.123 [main] INFO  middleware.resource - 开始初始化资源: test-redis-001
2025-01-28 10:30:45.124 [main] INFO  middleware.resource - 获取资源提供者: REDIS
2025-01-28 10:30:45.125 [main] INFO  middleware.resource - 验证资源配置
2025-01-28 10:30:45.126 [main] INFO  middleware.resource - 创建资源实例
2025-01-28 10:30:45.127 [main] INFO  middleware.resource - 初始化资源
2025-01-28 10:30:45.128 [main] INFO  middleware.resource - 启动资源
2025-01-28 10:30:45.129 [main] INFO  middleware.resource - 保存资源到注册表
2025-01-28 10:30:45.130 [main] INFO  middleware.resource - 资源初始化完成: test-redis-001
```

### Service生命周期日志
```
2025-01-28 10:31:00.123 [main] INFO  middleware.service - 开始初始化服务: test-kvs-service-001
2025-01-28 10:31:00.124 [main] INFO  middleware.service - 服务类型: KEY_VALUE_STORE, 支持的资源类别: REDIS
2025-01-28 10:31:00.125 [main] INFO  middleware.service - 获取关联资源: test-redis-001
2025-01-28 10:31:00.126 [main] INFO  middleware.service - 成功获取资源: test-redis-001
2025-01-28 10:31:00.127 [main] INFO  middleware.service - 获取服务提供者: KEY_VALUE_STORE
2025-01-28 10:31:00.128 [main] INFO  middleware.service - 验证服务配置
2025-01-28 10:31:00.129 [main] INFO  middleware.service - 创建服务实例
2025-01-28 10:31:00.130 [main] INFO  middleware.service - 初始化服务
2025-01-28 10:31:00.131 [main] INFO  middleware.service - 启动服务
2025-01-28 10:31:00.132 [main] INFO  middleware.service - 保存服务到注册表
2025-01-28 10:31:00.133 [main] INFO  middleware.service - 服务初始化完成: test-kvs-service-001
```

## 前端集成示例

### JavaScript EventSource示例
```javascript
// 连接Resource日志流
const resourceLogStream = new EventSource('/middleware/resource-runtime/logs-stream?resourceId=test-redis-001');

resourceLogStream.addEventListener('connected', function(event) {
    const historicalLogs = JSON.parse(event.data);
    console.log('历史日志:', historicalLogs);
});

resourceLogStream.addEventListener('append', function(event) {
    const newLog = JSON.parse(event.data);
    console.log('新日志:', newLog);
});

resourceLogStream.addEventListener('clean', function(event) {
    console.log('日志已清理');
});

// 连接Service日志流
const serviceLogStream = new EventSource('/middleware/service-runtime/logs-stream?serviceId=test-kvs-service-001');
// 事件处理同上
```

## 配置说明

### 日志收集器配置
- 默认最大日志条数：200条
- 日志格式：包含时间戳、线程、级别、Logger和消息
- 包名过滤：`com.siteweb.tcs.middleware`

### 性能考虑
- 使用CopyOnWriteArrayList管理SSE连接，支持并发访问
- 日志缓冲区采用LinkedList，自动清理超出限制的历史日志
- 组件销毁时自动清理对应的日志收集器

## 故障排除

### 常见问题

1. **无法获取日志流（返回400错误）**
   - 检查Resource ID或Service ID是否正确
   - 确认对应的组件是否已初始化
   - 验证日志收集器是否已创建

2. **日志流中断**
   - 检查网络连接
   - 确认SSE连接是否超时
   - 重新建立连接

3. **日志级别过滤不生效**
   - 检查Logback配置
   - 确认Logger级别设置
   - 验证包名过滤规则

### 调试建议
- 启用DEBUG级别日志查看详细的生命周期信息
- 使用浏览器开发者工具监控SSE连接状态
- 检查服务器日志中的错误信息

## 扩展功能

### 未来改进方向
1. 支持日志搜索和过滤
2. 添加日志导出功能
3. 实现日志持久化存储
4. 支持自定义日志格式
5. 添加日志统计和分析功能
