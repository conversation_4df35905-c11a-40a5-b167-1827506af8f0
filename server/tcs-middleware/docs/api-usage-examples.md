# 中间件日志API使用示例

## 概述

本文档提供了中间件日志查看API的详细使用示例，包括实时日志流和HTTP日志获取两种方式。

## API接口总览

### Resource日志接口
1. **实时日志流**: `GET /middleware/resource-runtime/logs-stream?resourceId={resourceId}`
2. **最近日志**: `GET /middleware/resource-runtime/logs/{resourceId}`

### Service日志接口
1. **实时日志流**: `GET /middleware/service-runtime/logs-stream?serviceId={serviceId}`
2. **最近日志**: `GET /middleware/service-runtime/logs/{serviceId}`

## 使用示例

### 1. 获取Resource最近100条日志

#### 请求示例
```bash
curl -X GET "http://localhost:8080/middleware/resource-runtime/logs/test-redis-001" \
  -H "Accept: application/json"
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "timeStamp": "2025-01-28T10:30:45.123",
      "level": "INFO",
      "loggerName": "middleware.resource",
      "message": "开始初始化资源: test-redis-001",
      "thread": "main",
      "componentId": "test-redis-001",
      "componentType": "resource"
    },
    {
      "timeStamp": "2025-01-28T10:30:45.124",
      "level": "INFO",
      "loggerName": "middleware.resource",
      "message": "获取资源提供者: REDIS",
      "thread": "main",
      "componentId": "test-redis-001",
      "componentType": "resource"
    },
    {
      "timeStamp": "2025-01-28T10:30:45.130",
      "level": "INFO",
      "loggerName": "middleware.resource",
      "message": "资源初始化完成: test-redis-001",
      "thread": "main",
      "componentId": "test-redis-001",
      "componentType": "resource"
    }
  ]
}
```

### 2. 获取Service最近100条日志

#### 请求示例
```bash
curl -X GET "http://localhost:8080/middleware/service-runtime/logs/test-kvs-service-001" \
  -H "Accept: application/json"
```

#### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "timeStamp": "2025-01-28T10:31:00.123",
      "level": "INFO",
      "loggerName": "middleware.service",
      "message": "开始初始化服务: test-kvs-service-001",
      "thread": "main",
      "componentId": "test-kvs-service-001",
      "componentType": "service"
    },
    {
      "timeStamp": "2025-01-28T10:31:00.124",
      "level": "INFO",
      "loggerName": "middleware.service",
      "message": "服务类型: KEY_VALUE_STORE, 支持的资源类别: REDIS",
      "thread": "main",
      "componentId": "test-kvs-service-001",
      "componentType": "service"
    },
    {
      "timeStamp": "2025-01-28T10:31:00.133",
      "level": "INFO",
      "loggerName": "middleware.service",
      "message": "服务初始化完成: test-kvs-service-001",
      "thread": "main",
      "componentId": "test-kvs-service-001",
      "componentType": "service"
    }
  ]
}
```

### 3. 实时日志流示例

#### Resource实时日志流
```bash
curl -N "http://localhost:8080/middleware/resource-runtime/logs-stream?resourceId=test-redis-001"
```

#### Service实时日志流
```bash
curl -N "http://localhost:8080/middleware/service-runtime/logs-stream?serviceId=test-kvs-service-001"
```

#### SSE事件格式
```
event: connected
data: [{"timeStamp":"2025-01-28T10:30:45.123","level":"INFO",...}]

event: append
data: {"timeStamp":"2025-01-28T10:30:46.123","level":"INFO",...}

event: clean
data: ""
```

## JavaScript前端集成

### 获取最近日志
```javascript
// 获取Resource最近日志
async function getResourceLogs(resourceId) {
    try {
        const response = await fetch(`/middleware/resource-runtime/logs/${resourceId}`);
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('Resource logs:', result.data);
            return result.data;
        } else {
            console.error('Failed to get logs:', result.message);
            return [];
        }
    } catch (error) {
        console.error('Error fetching logs:', error);
        return [];
    }
}

// 获取Service最近日志
async function getServiceLogs(serviceId) {
    try {
        const response = await fetch(`/middleware/service-runtime/logs/${serviceId}`);
        const result = await response.json();
        
        if (result.code === 200) {
            console.log('Service logs:', result.data);
            return result.data;
        } else {
            console.error('Failed to get logs:', result.message);
            return [];
        }
    } catch (error) {
        console.error('Error fetching logs:', error);
        return [];
    }
}

// 使用示例
getResourceLogs('test-redis-001').then(logs => {
    logs.forEach(log => {
        console.log(`[${log.timeStamp}] ${log.level}: ${log.message}`);
    });
});
```

### 实时日志流
```javascript
// 连接Resource实时日志流
function connectResourceLogStream(resourceId) {
    const eventSource = new EventSource(`/middleware/resource-runtime/logs-stream?resourceId=${resourceId}`);
    
    eventSource.addEventListener('connected', function(event) {
        const historicalLogs = JSON.parse(event.data);
        console.log('Historical logs received:', historicalLogs.length);
        displayLogs(historicalLogs);
    });
    
    eventSource.addEventListener('append', function(event) {
        const newLog = JSON.parse(event.data);
        console.log('New log:', newLog);
        appendLog(newLog);
    });
    
    eventSource.addEventListener('clean', function(event) {
        console.log('Logs cleaned');
        clearLogDisplay();
    });
    
    eventSource.onerror = function(event) {
        console.error('EventSource failed:', event);
    };
    
    return eventSource;
}

// 显示日志的辅助函数
function displayLogs(logs) {
    const logContainer = document.getElementById('log-container');
    logContainer.innerHTML = '';
    logs.forEach(log => appendLog(log));
}

function appendLog(log) {
    const logContainer = document.getElementById('log-container');
    const logElement = document.createElement('div');
    logElement.className = `log-entry log-${log.level.toLowerCase()}`;
    logElement.innerHTML = `
        <span class="timestamp">${log.timeStamp}</span>
        <span class="level">${log.level}</span>
        <span class="message">${log.message}</span>
    `;
    logContainer.appendChild(logElement);
    logContainer.scrollTop = logContainer.scrollHeight;
}

function clearLogDisplay() {
    const logContainer = document.getElementById('log-container');
    logContainer.innerHTML = '';
}
```

## 错误处理

### 常见错误响应

#### 日志收集器未找到
```json
{
  "code": 500,
  "message": "Resource log collector not found: test-redis-001",
  "data": null
}
```

#### 组件不存在
```json
{
  "code": 500,
  "message": "Service log collector not found: test-service-001",
  "data": null
}
```

### 错误处理建议
1. 检查Resource ID或Service ID是否正确
2. 确认对应的组件是否已初始化
3. 验证日志收集器是否已创建
4. 检查网络连接和服务状态

## 性能考虑

### HTTP日志接口
- 每次请求返回最近100条日志
- 响应时间通常在10-50ms
- 适合定期轮询或按需查询

### 实时日志流
- 基于SSE的长连接
- 实时推送新日志
- 适合实时监控场景
- 注意连接数限制和内存使用

## 最佳实践

1. **选择合适的接口**：
   - 实时监控使用SSE流接口
   - 历史查看使用HTTP接口

2. **错误处理**：
   - 始终检查响应状态码
   - 处理网络异常和超时

3. **性能优化**：
   - 避免频繁轮询HTTP接口
   - 合理管理SSE连接数量

4. **用户体验**：
   - 提供加载状态指示
   - 实现日志级别过滤
   - 支持日志搜索功能
