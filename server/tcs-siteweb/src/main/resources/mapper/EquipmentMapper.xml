<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EquipmentMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Equipment">
        <id column="EquipmentId" property="equipmentId" />
        <result column="EquipmentName" property="equipmentName" />
        <result column="EquipmentCategory" property="equipmentCategory" />
        <result column="EquipmentTemplateId" property="equipmentTemplateId" />
        <result column="MonitorUnitId" property="monitorUnitId" />
        <result column="StationId" property="stationId" />
        <result column="HouseId" property="houseId" />
        <result column="SamplerUnitId" property="samplerUnitId" />
        <result column="ResourceStructureId" property="resourceStructureId" />
        <result column="DisplayIndex" property="displayIndex" />
        <result column="UpdateTime" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        EquipmentId, EquipmentName, EquipmentCategory, EquipmentTemplateId, MonitorUnitId,
        StationId, HouseId, SamplerUnitId, ResourceStructureId, DisplayIndex, UpdateTime
    </sql>

    <!-- 根据分类映射更新设备分类 -->
    <update id="updateEquipmentCategoryByCategoryIdMap">
        UPDATE tbl_equipment e
        SET e.EquipmentCategory = (
            SELECT m.BusinessCategoryId
            FROM tbl_categoryidmap m
            WHERE m.BusinessId = #{businessId}
            AND m.CategoryTypeId = #{categoryTypeId}
            AND m.OriginalCategoryId = e.EquipmentCategory
        )
        WHERE e.EquipmentId = #{equipmentId}
        AND EXISTS (
            SELECT 1
            FROM tbl_categoryidmap m
            WHERE m.BusinessId = #{businessId}
            AND m.CategoryTypeId = #{categoryTypeId}
            AND m.OriginalCategoryId = e.EquipmentCategory
        )
    </update>
</mapper>
