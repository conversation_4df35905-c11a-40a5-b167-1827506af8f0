<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.StationMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Station">
        <id column="StationId" property="stationId" />
        <result column="StationName" property="stationName" />
        <result column="Latitude" property="latitude" />
        <result column="Longitude" property="longitude" />
        <result column="SetupTime" property="setupTime" />
        <result column="CompanyId" property="companyId" />
        <result column="ConnectState" property="connectState" />
        <result column="UpdateTime" property="updateTime" />
        <result column="StationCategory" property="stationCategory" />
        <result column="StationGrade" property="stationGrade" />
        <result column="StationState" property="stationState" />
        <result column="ContactId" property="contactId" />
        <result column="SupportTime" property="supportTime" />
        <result column="OnWayTime" property="onWayTime" />
        <result column="SurplusTime" property="surplusTime" />
        <result column="FloorNo" property="floorNo" />
        <result column="PropList" property="propList" />
        <result column="Acreage" property="acreage" />
        <result column="BuildingType" property="buildingType" />
        <result column="ContainNode" property="containNode" />
        <result column="Description" property="description" />
        <result column="BordNumber" property="bordNumber" />
        <result column="CenterId" property="centerId" />
        <result column="Enable" property="enable" />
        <result column="StartTime" property="startTime" />
        <result column="EndTime" property="endTime" />
        <result column="ProjectName" property="projectName" />
        <result column="ContractNo" property="contractNo" />
        <result column="InstallTime" property="installTime" />
        <result column="maskStartTime" property="maskStartTime" />
        <result column="maskEndTime" property="maskEndTime" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ts.StationId,
        ts.StationName,
        ts.Latitude,
        ts.Longitude,
        ts.SetupTime,
        ts.CompanyId,
        ts.ConnectState,
        ts.UpdateTime,
        ts.StationCategory,
        ts.StationGrade,
        ts.StationState,
        ts.ContactId,
        ts.SupportTime,
        ts.OnwayTime,
        ts.SurplusTime,
        ts.FloorNo,
        ts.PropList,
        ts.Acreage,
        ts.BuildingType,
        ts.ContainNode,
        ts.Description,
        ts.BordNumber,
        ts.CenterId,
        ts.Enable,
        ts.StartTime,
        ts.EndTime,
        ts2.ProjectName,
        ts2.ContractNo,
        ts.InstallTime,
        ts3.StartTime as maskStartTime,
        ts3.EndTime as maskEndTime
    </sql>

    <!-- 查询基础SQL -->
    <sql id="findStationsQuery">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            tbl_station ts
        LEFT JOIN 
            tbl_stationprojectinfo ts2 ON ts.StationId = ts2.StationId
        LEFT JOIN 
            tbl_stationmask ts3 ON ts.StationId = ts3.StationId
    </sql>

    <!-- 根据站点ID查找站点 -->
    <select id="findStationById" resultMap="BaseResultMap">
        <include refid="findStationsQuery"/> WHERE ts.StationId = #{stationId}
    </select>
</mapper>
