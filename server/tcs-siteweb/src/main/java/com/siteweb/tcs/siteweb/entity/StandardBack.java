package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Standard back entity
 */
@Data
@TableName("tbl_standardback")
public class StandardBack implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "EntryCategory")
    private Integer entryCategory;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("EntryId")
    private Integer entryId;

    @TableField("EventConditionId")
    private Integer eventConditionId;

    @TableField("SignalName")
    private String signalName;

    @TableField("StoreInterval")
    private Double storeInterval;

    @TableField("AbsValueThreshold")
    private Double absValueThreshold;

    @TableField("PercentThreshold")
    private Double percentThreshold;

    @TableField("EventName")
    private String eventName;

    @TableField("EventSeverity")
    private Integer eventSeverity;

    @TableField("StartCompareValue")
    private Double startCompareValue;

    @TableField("StartDelay")
    private Integer startDelay;

    @TableField("StandardName")
    private Integer standardName;

    @TableField("Meanings")
    private String meanings;

    @TableField("ControlName")
    private String controlName;
}
