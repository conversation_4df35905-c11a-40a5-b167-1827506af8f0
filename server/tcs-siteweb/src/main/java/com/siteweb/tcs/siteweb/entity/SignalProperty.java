package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Signal property entity
 */
@Data
@TableName("tbl_signalproperty")
public class SignalProperty implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("SignalId")
    private Integer signalId;

    @TableField("SignalPropertyId")
    private Integer signalPropertyId;
}
