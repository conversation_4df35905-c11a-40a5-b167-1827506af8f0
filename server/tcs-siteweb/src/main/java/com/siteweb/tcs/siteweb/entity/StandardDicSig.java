package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Standard dictionary signal entity
 */
@Data
@TableName("tbl_standarddicsig")
public class StandardDicSig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StandardDicId")
    private Integer standardDicId;

    @TableField("StandardType")
    private Integer standardType;

    @TableField("EquipmentLogicClassId")
    private Integer equipmentLogicClassId;

    @TableField("EquipmentLogicClass")
    private String equipmentLogicClass;

    @TableField("SignalLogicClassId")
    private Integer signalLogicClassId;

    @TableField("SignalLogicClass")
    private String signalLogicClass;

    @TableField("SignalStandardName")
    private String signalStandardName;

    @TableField("NetManageId")
    private String netManageId;

    @TableField("StoreInterval")
    private Integer storeInterval;

    @TableField("AbsValueThreshold")
    private Double absValueThreshold;

    @TableField("StatisticsPeriod")
    private Integer statisticsPeriod;

    @TableField("PercentThreshold")
    private Double percentThreshold;

    @TableField("StationCategory")
    private Integer stationCategory;

    @TableField("ModifyType")
    private Integer modifyType;

    @TableField("Description")
    private String description;

    @TableField("ExtendFiled1")
    private String extendFiled1;

    @TableField("ExtendFiled2")
    private String extendFiled2;
}
