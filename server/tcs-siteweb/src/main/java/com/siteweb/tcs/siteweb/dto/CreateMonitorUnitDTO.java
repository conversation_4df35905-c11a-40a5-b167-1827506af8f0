package com.siteweb.tcs.siteweb.dto;

import lombok.Data;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * DTO for creating a new monitor unit
 */
@Data
public class CreateMonitorUnitDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 资源结构ID
     */
    private Integer resourceStructureId;

    /**
     * 监控单元名称
     */
    @Size(max = 100, message = "监控单元名称长度不能超过100")
    private String monitorUnitName;
    
    /**
     * IP地址
     */
    private String ipAddress;
    
    /**
     * 监控单元类别
     */
    private Integer monitorUnitCategory;

    /**
     * 描述
     */
    private String description;

    /**
     * RDS服务器
     */
    private String rdsServer;
    
    /**
     * 数据服务器
     */
    private String dataServer;
    
    /**
     * 工作站ID
     */
    private Integer workStationId;

    /**
     * 合同编号
     */
    private String contractNo;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 站点ID
     */
    private Integer stationId;

    /**
     * 监控单元代码
     */
    private String monitorUnitCode;

    /**
     * 运行模式
     */
    private Integer runMode;

    /**
     * 配置文件代码
     */
    private String configFileCode;

    /**
     * 采样配置代码
     */
    private String sampleConfigCode;

    /**
     * 软件版本
     */
    private String softwareVersion;

    /**
     * 应用配置ID
     */
    private Integer appCongfigId;

    /**
     * 是否可分发
     */
    private Boolean canDistribute;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 是否为FSU
     */
    private Boolean fsu;
    
    /**
     * 是否删除关联设备（用于删除操作，仅在代码内使用，不会序列化）
     */
    private transient Boolean isDelEqs;

    /**
     * Convert to MonitorUnitDTO
     *
     * @param stationId Station ID
     * @return MonitorUnitDTO
     */
    public MonitorUnitDTO toMonitorUnit(Integer stationId) {
        MonitorUnitDTO monitorUnit = new MonitorUnitDTO();
        monitorUnit.setStationId(stationId != null ? stationId : this.stationId);
        monitorUnit.setMonitorUnitName(this.monitorUnitName);
        monitorUnit.setIpAddress(this.ipAddress);
        monitorUnit.setMonitorUnitCategory(this.monitorUnitCategory);
        monitorUnit.setDescription(this.description);
        monitorUnit.setMonitorUnitCode(this.monitorUnitCode);
        monitorUnit.setRunMode(this.runMode != null ? this.runMode : 1);
        monitorUnit.setConfigFileCode(this.configFileCode);
        monitorUnit.setSampleConfigCode(this.sampleConfigCode);
        monitorUnit.setWorkStationId(this.workStationId);
        monitorUnit.setCanDistribute(this.canDistribute != null ? this.canDistribute : true);
        monitorUnit.setIsSync(false);
        monitorUnit.setIsConfigOK(false);
        monitorUnit.setAppConfigId(this.appCongfigId != null ? this.appCongfigId : 1);
        monitorUnit.setEnable(this.enable != null ? this.enable : true);
        monitorUnit.setFsu(this.fsu != null ? this.fsu : false);
        monitorUnit.setSoftwareVersion(this.softwareVersion != null ? this.softwareVersion : "");
        monitorUnit.setConnectState(2);
        monitorUnit.setProjectName(this.projectName);
        monitorUnit.setContractNo(this.contractNo);
        monitorUnit.setInstallTime(LocalDateTime.now());
        return monitorUnit;
    }
} 