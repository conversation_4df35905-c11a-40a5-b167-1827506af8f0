package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.EventEx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Event Extension Mapper
 */
@Mapper
public interface EventExMapper extends BaseMapper<EventEx> {
    
    /**
     * 根据设备模板ID和事件ID删除事件扩展信息
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 删除结果
     */
    int deleteByEvent(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);
    
    /**
     * 批量更新事件扩展信息
     * @param eventExList 事件扩展信息列表
     */
    void batchUpdate(@Param("eventExList") List<EventEx> eventExList);
    
    /**
     * 根据设备模板ID和事件ID查询事件扩展信息
     * @param equipmentTemplateId 设备模板ID
     * @param eventId 事件ID
     * @return 事件扩展信息
     */
    EventEx findByEventId(@Param("equipmentTemplateId") Integer equipmentTemplateId, @Param("eventId") Integer eventId);
}
