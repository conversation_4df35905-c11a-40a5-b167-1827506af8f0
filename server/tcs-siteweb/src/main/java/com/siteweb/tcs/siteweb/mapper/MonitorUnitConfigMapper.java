package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.MonitorUnitConfig;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Monitor Unit Config Mapper
 */
@Mapper
@Repository
public interface MonitorUnitConfigMapper extends BaseMapper<MonitorUnitConfig> {

    /**
     * 根据监控单元ID查找配置
     *
     * @param monitorUnitId 监控单元ID
     * @return 监控单元配置列表
     */
    List<MonitorUnitConfig> findByMonitorUnitId(Integer monitorUnitId);
}
