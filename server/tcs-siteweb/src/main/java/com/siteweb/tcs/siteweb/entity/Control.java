package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Control entity
 */
@Data
@TableName("tbl_control")
public class Control implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Integer id;

    @TableField("EquipmentTemplateId")
    private Integer equipmentTemplateId;

    @TableField("ControlId")
    private Integer controlId;

    @TableField("ControlName")
    private String controlName;

    @TableField("ControlCategory")
    private Integer controlCategory;

    @TableField("CmdToken")
    private String cmdToken;

    @TableField("BaseTypeId")
    private Long baseTypeId;

    @TableField("ControlSeverity")
    private Integer controlSeverity;

    @TableField("SignalId")
    private Integer signalId;

    @TableField("TimeOut")
    private Double timeOut;

    @TableField("Retry")
    private Integer retry;

    @TableField("Description")
    private String description;

    @TableField("Enable")
    private Boolean enable;

    @TableField("Visible")
    private Boolean visible;

    @TableField("DisplayIndex")
    private Integer displayIndex;

    @TableField("CommandType")
    private Integer commandType;

    @TableField("ControlType")
    private Short controlType;

    @TableField("DataType")
    private Short dataType;

    @TableField("MaxValue")
    private Double maxValue;

    @TableField("MinValue")
    private Double minValue;

    @TableField("DefaultValue")
    private Double defaultValue;

    @TableField("ModuleNo")
    private Integer moduleNo;
}
