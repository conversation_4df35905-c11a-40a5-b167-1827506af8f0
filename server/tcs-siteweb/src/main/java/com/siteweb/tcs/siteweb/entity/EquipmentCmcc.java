package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备实体类 (CMCC)
 */
@Data
@TableName("tbl_equipmentcmcc")
public class EquipmentCmcc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID (复合主键)
     */
    @TableField(value = "StationId")
    private Integer stationId;

    /**
     * 监控单元ID
     */
    @TableField("MonitorUnitId")
    private Integer monitorUnitId;

    /**
     * 设备ID (复合主键的一部分)
     */
    @TableField("EquipmentId") // Removed @TableId, assuming composite key handled elsewhere or StationId is primary
    private Integer equipmentId;

    @TableField("DeviceID")
    private String deviceId;

    @TableField("DeviceName")
    private String deviceName;

    @TableField("FSUID")
    private String fsuid;

    @TableField("SiteID")
    private String siteId;

    @TableField("SiteName")
    private String siteName;

    @TableField("RoomID")
    private String roomId;

    @TableField("RoomName")
    private String roomName;

    @TableField("DeviceType")
    private Integer deviceType;

    @TableField("DeviceSubType")
    private Integer deviceSubType;

    @TableField("Model")
    private String model;

    @TableField("Brand")
    private String brand;

    @TableField("RatedCapacity")
    private Double ratedCapacity;

    @TableField("Version")
    private String version;

    @TableField("BeginRunTime")
    private LocalDateTime beginRunTime;

    @TableField("DevDescribe")
    private String devDescribe;

    @TableField("ExtendField1")
    private String extendField1;

    @TableField("ExtendField2")
    private String extendField2;
}