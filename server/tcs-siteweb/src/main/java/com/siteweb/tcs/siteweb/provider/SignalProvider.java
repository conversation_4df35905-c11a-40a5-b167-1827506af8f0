package com.siteweb.tcs.siteweb.provider;

import com.siteweb.tcs.siteweb.dto.SignalConfigItem;
import com.siteweb.tcs.siteweb.entity.Signal;
import com.siteweb.tcs.siteweb.service.ISignalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Signal Provider (业务逻辑类)
 */
@Slf4j
@Service
public class SignalProvider {

    @Autowired
    private ISignalService signalService;

    public SignalConfigItem createSignal(SignalConfigItem signalConfigItem) {
        try {
            Signal signal = signalService.createSignal(signalConfigItem);
            if (signal != null) {
                // 返回原始的SignalConfigItem，因为它在createSignal方法中已经更新了ID
                return signalConfigItem;
            }
            return null;
        } catch (Exception e) {
            log.error("Failed to create signal", e);
            return null;
        }
    }

    public Signal updateSignal(SignalConfigItem signalConfigItem) {
        try {
            return signalService.updateSignal(signalConfigItem);
        } catch (Exception e) {
            log.error("Failed to update signal", e);
            return null;
        }
    }

    public boolean deleteSignal(int equipmentTemplateId, int signalId) {
        try {
            int result = signalService.deleteSignal(equipmentTemplateId, signalId);
            return result > 0;
        } catch (Exception e) {
            log.error("Failed to delete signal", e);
            return false;
        }
    }

    public int batchDeleteSignal(int equipmentTemplateId, List<Integer> signalIds) {
        try {
            return signalService.batchDeleteSignal(equipmentTemplateId, signalIds);
        } catch (Exception e) {
            log.error("Failed to batch delete signals", e);
            return 0;
        }
    }

    public List<Signal> getByEquipmentTemplateId(Integer equipmentTemplateId) {
        try {
            return signalService.findByEquipmentTemplateId(equipmentTemplateId);
        } catch (Exception e) {
            log.error("Failed to get signals by equipment template ID", e);
            return null;
        }
    }

    public SignalConfigItem getSignalDetail(Integer equipmentTemplateId, Integer signalId) {
        try {
            SignalConfigItem signalConfigItem = signalService.findByEquipmentTemplateIdAndSignalId(equipmentTemplateId, signalId);
            return signalConfigItem;
        } catch (Exception e) {
            log.error("Failed to get signal detail", e);
            return null;
        }
    }
} 