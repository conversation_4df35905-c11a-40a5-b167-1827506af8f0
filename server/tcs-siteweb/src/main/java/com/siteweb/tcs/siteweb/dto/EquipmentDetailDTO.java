package com.siteweb.tcs.siteweb.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备详情DTO
 */
@Data
public class EquipmentDetailDTO {
    private Integer equipmentId;
    private String equipmentName;
    private String equipmentNo;
    private String equipmentModule;
    private String equipmentStyle;
    private Integer assetState;
    private Double price;
    private Double usedLimit;
    private LocalDateTime usedDate;
    private LocalDateTime buyDate;
    private String vendor;
    private String unit;
    private Integer equipmentCategory;
    private Integer equipmentType;
    private Integer equipmentClass;
    private Integer equipmentState;
    private String eventExpression;
    private Double startDelay;
    private Double endDelay;
    private String property;
    private String description;
    private Integer equipmentTemplateId;
    private Integer houseId;
    private Integer monitorUnitId;
    private Integer workStationId;
    private Integer samplerUnitId;
    private Integer displayIndex;
    private Integer connectState;
    private LocalDateTime updateTime;
    private String parentEquipmentId;
    private String ratedCapacity;
    private String installedModule;
    private Integer resourceStructureId;
    private Integer stationId;
    private String extValue;
    private String photo;
    
    /**
     * 设备工程信息
     */
    @Data
    public static class EquipmentProjectInfo {
        private String projectName;
        private String contractNo;
        private LocalDateTime installTime;
        private String equipmentSN;
        private String so;
    }
    
    private EquipmentProjectInfo equipmentProjectInfo;
}
