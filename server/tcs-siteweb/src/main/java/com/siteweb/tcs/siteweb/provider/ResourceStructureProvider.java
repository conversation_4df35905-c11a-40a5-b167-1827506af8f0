package com.siteweb.tcs.siteweb.provider;

import com.siteweb.tcs.siteweb.cache.ResourceStructureCache;
import com.siteweb.tcs.siteweb.dto.StructureTreeNodeDTO;
import com.siteweb.tcs.siteweb.manager.StructureTreeManager;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

/**
 * Resource Structure Provider (业务逻辑类)
 */
@Slf4j
@Service
public class ResourceStructureProvider {

    @Autowired
    private StructureTreeManager structureTreeManager;
    @Autowired
    private ResourceStructureCache resourceStructureCache;
    @Autowired
    private MessageSource messageSource;

    @Autowired
    private IResourceStructureService resourceStructureService;

    public StructureTreeNodeDTO getTree() {
        try {
            return getTree(null, null, null);
        } catch (Exception e) {
            log.error("Error fetching resource structure tree branch");
            return null;
        }
    }

    /**
     * 获取结构树分支
     * @param structureID 结构节点ID，可为null表示根
     * @param depth 递归深度，可为null
     * @param eqs 是否包含设备，可为null
     * @return 分支结构
     */
    public StructureTreeNodeDTO getTree(Integer structureID, Integer depth, Boolean eqs) {
        try {
            return structureTreeManager.enumTreeBranch(structureID, eqs, depth);
        } catch (Exception e) {
            log.error("Error fetching resource structure tree branch for rid {}: {}", structureID, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取结构树根节点
     * @return 根节点列表
     */
    public Integer getTreeRootId() {
        try {
            return resourceStructureService.getTreeRootId();
        } catch (Exception e) {
            log.error("Error fetching resource structure tree roots: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 重新加载结构树缓存
     * @return 操作结果信息
     */
    public boolean reloadTree() {
        try {
            resourceStructureCache.reload();
            structureTreeManager.reload();
            return true;
        } catch (Exception e) {
            log.error("Error reloading resource structure cache: {}", e.getMessage(), e);
            return false;
        }
    }
} 