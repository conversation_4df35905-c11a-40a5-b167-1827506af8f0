package com.siteweb.tcs.south.seed.exception;

import com.siteweb.tcs.common.exception.code.PluginErrorCode;
import lombok.Getter;

/**
 * 实现一个插件业务的异常类（例子）
 *
 * <AUTHOR> (2025-04-23)
 **/
public enum SeedException implements PluginErrorCode {

    /**
     * 例子
     */
    INITIALIZATION_DEVICE_FAILED("INITIALIZATION_DEVICE_FAILED", "设备初始化失败"), //
    ;

    SeedException(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Getter
    private String code;


    @Getter
    private String message;

}
