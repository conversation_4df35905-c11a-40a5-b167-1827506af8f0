package com.siteweb.tcs.south.seed.web.service.impl;

import com.siteweb.tcs.south.seed.connector.ConnectorDataHolder;
import com.siteweb.tcs.south.seed.web.service.IHelloSeedService;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.pattern.Patterns;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.CompletionStage;

@Service
public class HelloSeedServiceImpl implements IHelloSeedService {

    @Autowired
    private ConnectorDataHolder connectorDataHolder;

    public String sayHello() {
        ActorRef rootActor = connectorDataHolder.getRootActor();
        if (rootActor != null) {
            CompletionStage<Object> ask = Patterns.ask(rootActor, "hello", Duration.ofSeconds(5));
            try {
                Object result = ask.toCompletableFuture().get();
                return result != null ? result.toString() : "No response";
            } catch (Exception e) {
                return "Error: " + e.getMessage();
            }
        }
        return "RootActor not found";
    }
}
