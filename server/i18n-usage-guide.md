# i18n 命名空间使用指南

## 1. 简介

本指南介绍如何在项目中使用基于命名空间的国际化（i18n）系统，以避免不同模块之间的翻译键冲突。通过遵循本指南中的规范和最佳实践，开发人员可以确保各模块的 i18n 资源独立管理且不会相互干扰。

## 2. 命名空间规范

### 2.1 命名空间格式

每个模块的 i18n 键应遵循以下格式：

```
{moduleNamespace}.{category}.{key}
```

例如：`south.seed.common.add`，其中：
- `south.seed` 是模块命名空间
- `common` 是分类
- `add` 是具体的键名

### 2.2 模块命名空间分配

以下是项目中各模块的命名空间分配：

| 模块名称 | 命名空间前缀 |
|---------|------------|
| tcs-core | core |
| tcs-hub | hub |
| tcs-south-seed | south.seed |
| tcs-north-etl | north.etl |
| tcs-tracking | tracking |
| ... | ... |

如需为新模块分配命名空间，请遵循现有的命名模式，并确保与已有命名空间不冲突。

## 3. 前端 i18n 使用指南（Vue.js）

### 3.1 创建模块 i18n 资源

在模块的 `src/main/web/src/i18n/` 目录下创建语言文件：

```typescript
// zh-CN.ts
export default {
  'your.module': {  // 使用分配给你模块的命名空间
    'category': {
      'key1': '翻译1',
      'key2': '翻译2'
    }
  }
}

// en-US.ts
export default {
  'your.module': {  // 使用分配给你模块的命名空间
    'category': {
      'key1': 'Translation 1',
      'key2': 'Translation 2'
    }
  }
}
```

### 3.2 注册模块 i18n 资源

在模块的入口文件（通常是 `index.ts`）中注册 i18n 资源：

```typescript
import { createI18n } from 'vue-i18n'
import zhCN from './i18n/zh-CN'
import enUS from './i18n/en-US'

const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {
    'zh-CN': zhCN,
    'en-US': enUS
  }
})

// 使用 i18n
app.use(i18n)
```

### 3.3 在组件中使用 i18n

直接使用带命名空间的完整键路径：

```vue
<template>
  <div>
    <h1>{{ $t('your.module.category.key1') }}</h1>
    <p>{{ $t('your.module.category.key2') }}</p>
  </div>
</template>
```

### 3.4 使用命名空间辅助函数（推荐）

创建一个命名空间辅助函数，简化 i18n 的使用：

```typescript
// i18n-namespace.ts
import { useI18n } from 'vue-i18n'

export function useNamespacedI18n(namespace: string) {
  const { t } = useI18n()
  
  return {
    t: (key: string, ...args: any[]) => {
      return t(`${namespace}.${key}`, ...args)
    }
  }
}
```

在组件中使用：

```vue
<script setup>
import { useNamespacedI18n } from '@/utils/i18n-namespace'

const { t } = useNamespacedI18n('your.module')
</script>

<template>
  <div>
    <h1>{{ t('category.key1') }}</h1>
    <p>{{ t('category.key2') }}</p>
  </div>
</template>
```

## 4. 后端 i18n 使用指南（Spring Boot）

### 4.1 创建模块 i18n 资源

在模块的 `src/main/resources/i18n/` 目录下创建属性文件：

```properties
# messages.properties (默认语言)
your.module.category.key1=Default translation 1
your.module.category.key2=Default translation 2

# messages_zh_CN.properties
your.module.category.key1=翻译1
your.module.category.key2=翻译2

# messages_en_US.properties
your.module.category.key1=Translation 1
your.module.category.key2=Translation 2
```

### 4.2 直接使用 MessageSource

```java
@Autowired
private MessageSource messageSource;

public void someMethod() {
    // 使用完整的命名空间键
    String message = messageSource.getMessage("your.module.category.key1", null, Locale.getDefault());
    // ...
}
```

### 4.3 使用命名空间辅助类（推荐）

创建一个命名空间辅助类，简化 i18n 的使用：

```java
// NamespacedMessageSource.java
@Component
public class NamespacedMessageSource {
    
    private final MessageSource messageSource;
    private final String namespace;
    
    public NamespacedMessageSource(MessageSource messageSource, String namespace) {
        this.messageSource = messageSource;
        this.namespace = namespace;
    }
    
    public String getMessage(String code) {
        return getMessage(code, null);
    }
    
    public String getMessage(String code, Object[] args) {
        return messageSource.getMessage(namespace + "." + code, args, Locale.getDefault());
    }
    
    public String getMessage(String code, Object[] args, Locale locale) {
        return messageSource.getMessage(namespace + "." + code, args, locale);
    }
}
```

在服务或控制器中使用：

```java
@Service
public class YourModuleService {
    
    private final NamespacedMessageSource messages;
    
    @Autowired
    public YourModuleService(MessageSource messageSource) {
        // 使用分配给你模块的命名空间
        this.messages = new NamespacedMessageSource(messageSource, "your.module");
    }
    
    public void someMethod() {
        // 使用简化的键（无需重复命名空间）
        String message = messages.getMessage("category.key1");
        // ...
    }
}
```

## 5. 最佳实践

### 5.1 键名组织

- 使用有意义的分类组织翻译键，如 `common`, `error`, `menu`, `form` 等
- 保持键名的一致性和可预测性
- 避免过深的嵌套层次，通常不超过 3 层

### 5.2 翻译内容

- 保持翻译文本简洁明了
- 对于包含变量的翻译，使用占位符（如 `{0}`, `{1}` 或具名参数）
- 确保所有支持的语言都有对应的翻译

### 5.3 代码组织

- 将 i18n 资源文件放在模块的标准位置
- 使用命名空间辅助函数/类简化代码
- 避免硬编码文本，始终使用 i18n 系统

### 5.4 维护和更新

- 定期检查是否有缺失的翻译
- 在添加新功能时同时添加相应的翻译
- 使用工具检测潜在的键冲突

## 6. 常见问题解答

### 6.1 如何处理跨模块共享的翻译？

对于多个模块共享的通用翻译，可以：
1. 将它们放在 `core` 模块中，如 `core.common.save`
2. 每个模块维护自己的副本，使用模块特定的命名空间

### 6.2 如何处理动态键名？

对于需要动态构建的键名，可以：

```typescript
// 前端
const dynamicKey = `your.module.${category}.${key}`;
const translation = i18n.global.t(dynamicKey);
```

```java
// 后端
String dynamicKey = "your.module." + category + "." + key;
String translation = messageSource.getMessage(dynamicKey, null, Locale.getDefault());
```

### 6.3 如何处理带参数的翻译？

```typescript
// 前端
const message = i18n.global.t('your.module.greeting', { name: 'John' });
// 或使用数组参数
const message = i18n.global.t('your.module.greeting', ['John']);
```

```java
// 后端
String message = messageSource.getMessage("your.module.greeting", new Object[]{"John"}, Locale.getDefault());
```

### 6.4 如何添加新的语言支持？

1. 创建新的语言文件，如 `ja-JP.ts` 或 `messages_ja_JP.properties`
2. 在 i18n 配置中注册新语言
3. 确保所有现有的翻译键都有对应的新语言翻译

## 7. 工具和资源

- [Vue I18n 官方文档](https://vue-i18n.intlify.dev/)
- [Spring Boot MessageSource 文档](https://docs.spring.io/spring-framework/docs/current/reference/html/core.html#context-functionality-messagesource)
- 推荐的 i18n 管理工具：
  - [BabelEdit](https://www.codeandweb.com/babeledit)
  - [i18n Manager](https://github.com/gilmarsquinelato/i18n-manager)
  - [POEditor](https://poeditor.com/)

## 8. 示例代码

### 8.1 前端完整示例

```typescript
// i18n/zh-CN.ts
export default {
  'south.seed': {
    'common': {
      'add': '添加',
      'edit': '编辑',
      'delete': '删除',
      'save': '保存',
      'cancel': '取消'
    },
    'device': {
      'title': '设备管理',
      'name': '设备名称',
      'status': '设备状态',
      'type': '设备类型',
      'createSuccess': '设备 {name} 创建成功'
    }
  }
}

// utils/i18n-namespace.ts
import { useI18n } from 'vue-i18n'

export function useNamespacedI18n(namespace: string) {
  const { t } = useI18n()
  
  return {
    t: (key: string, ...args: any[]) => {
      return t(`${namespace}.${key}`, ...args)
    }
  }
}

// DeviceList.vue
<script setup>
import { useNamespacedI18n } from '@/utils/i18n-namespace'

const { t } = useNamespacedI18n('south.seed')

function createDevice(name) {
  // ...
  return t('device.createSuccess', { name })
}
</script>

<template>
  <div>
    <h1>{{ t('device.title') }}</h1>
    <table>
      <tr>
        <th>{{ t('device.name') }}</th>
        <th>{{ t('device.status') }}</th>
        <th>{{ t('device.type') }}</th>
      </tr>
      <!-- ... -->
    </table>
    <button>{{ t('common.add') }}</button>
  </div>
</template>
```

### 8.2 后端完整示例

```java
// NamespacedMessageSource.java
@Component
public class NamespacedMessageSource {
    
    private final MessageSource messageSource;
    private final String namespace;
    
    public NamespacedMessageSource(MessageSource messageSource, String namespace) {
        this.messageSource = messageSource;
        this.namespace = namespace;
    }
    
    public String getMessage(String code) {
        return getMessage(code, null);
    }
    
    public String getMessage(String code, Object[] args) {
        return messageSource.getMessage(namespace + "." + code, args, Locale.getDefault());
    }
}

// DeviceService.java
@Service
public class DeviceService {
    
    private final NamespacedMessageSource messages;
    private final DeviceRepository deviceRepository;
    
    @Autowired
    public DeviceService(MessageSource messageSource, DeviceRepository deviceRepository) {
        this.messages = new NamespacedMessageSource(messageSource, "south.seed");
        this.deviceRepository = deviceRepository;
    }
    
    public Device createDevice(DeviceDTO deviceDTO) {
        // ...
        log.info(messages.getMessage("device.createSuccess", new Object[]{deviceDTO.getName()}));
        return savedDevice;
    }
    
    public List<Device> getAllDevices() {
        // ...
        if (devices.isEmpty()) {
            log.warn(messages.getMessage("device.notFound"));
        }
        return devices;
    }
}
```
