package com.siteweb.stream.service.controller;

import com.siteweb.stream.core.manager.StreamPluginManager;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Stream插件管理控制器
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@RestController
@RequestMapping("/stream/plugins")
public class StreamPluginController {
    
    private static final Logger logger = LoggerFactory.getLogger(StreamPluginController.class);
    
    @Autowired
    private StreamPluginManager streamPluginManager;
    
    /**
     * 获取所有Stream插件信息
     */
    @GetMapping(value = "/info", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllPluginInfos() {
        try {
            var pluginInfos = streamPluginManager.getAllPluginInfos();
            logger.info("Retrieved {} stream plugin infos", pluginInfos.size());
            return ResponseHelper.successful(pluginInfos);
        } catch (Exception e) {
            logger.error("Failed to get stream plugin infos", e);
            return ResponseHelper.failed("Failed to get stream plugin infos: " + e.getMessage());
        }
    }
    
    /**
     * 获取Stream插件系统统计信息
     */
    @GetMapping(value = "/stats", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPluginStats() {
        try {
            var stats = streamPluginManager.getPluginStats();
            logger.info("Retrieved stream plugin stats: {}", stats);
            return ResponseHelper.successful(stats);
        } catch (Exception e) {
            logger.error("Failed to get stream plugin stats", e);
            return ResponseHelper.failed("Failed to get stream plugin stats: " + e.getMessage());
        }
    }
    
    /**
     * 获取特定插件信息
     */
    @GetMapping(value = "/{pluginId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPluginInfo(@PathVariable String pluginId) {
        try {
            var pluginInfo = streamPluginManager.getPluginInfo(pluginId);
            if (pluginInfo != null) {
                return ResponseHelper.successful(pluginInfo);
            } else {
                return ResponseHelper.failed("Plugin not found: " + pluginId);
            }
        } catch (Exception e) {
            logger.error("Failed to get plugin info for: {}", pluginId, e);
            return ResponseHelper.failed("Failed to get plugin info: " + e.getMessage());
        }
    }
    
    /**
     * 重新加载插件信息
     */
    @PostMapping(value = "/reload", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> reloadPlugins() {
        try {
            streamPluginManager.reloadPluginInfos();
            var stats = streamPluginManager.getPluginStats();
            logger.info("Stream plugins reloaded successfully");
            return ResponseHelper.successful(stats);
        } catch (Exception e) {
            logger.error("Failed to reload stream plugins", e);
            return ResponseHelper.failed("Failed to reload stream plugins: " + e.getMessage());
        }
    }
    
    /**
     * 启动特定插件
     */
    @PostMapping(value = "/{pluginId}/start", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> startPlugin(@PathVariable String pluginId) {
        try {
            var state = streamPluginManager.startPlugin(pluginId);
            logger.info("Plugin {} started with state: {}", pluginId, state);
            return ResponseHelper.successful("Plugin started: " + state);
        } catch (Exception e) {
            logger.error("Failed to start plugin: {}", pluginId, e);
            return ResponseHelper.failed("Failed to start plugin: " + e.getMessage());
        }
    }
    
    /**
     * 停止特定插件
     */
    @PostMapping(value = "/{pluginId}/stop", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> stopPlugin(@PathVariable String pluginId) {
        try {
            var state = streamPluginManager.stopPlugin(pluginId);
            logger.info("Plugin {} stopped with state: {}", pluginId, state);
            return ResponseHelper.successful("Plugin stopped: " + state);
        } catch (Exception e) {
            logger.error("Failed to stop plugin: {}", pluginId, e);
            return ResponseHelper.failed("Failed to stop plugin: " + e.getMessage());
        }
    }
}
