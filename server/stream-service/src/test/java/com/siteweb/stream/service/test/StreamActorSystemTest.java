package com.siteweb.stream.service.test;

import com.siteweb.stream.common.stream.AkkaSystemEnvironment;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.ActorSystem;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Stream Actor System 集成测试
 * 验证 Stream 模块是否正确使用 TCS 的 Actor System
 */
@SpringBootTest
@Slf4j
public class StreamActorSystemTest {
    
    @Autowired
    @Qualifier("actorSystem")
    private ActorSystem tcsActorSystem;
    
    @Autowired
    @Qualifier("streamActorSystem")
    private ActorSystem streamActorSystem;
    
    @Autowired
    @Qualifier("stream-root-actor")
    private ActorRef streamRootActor;
    
    /**
     * 测试 Stream 模块是否正确使用 TCS 的 Actor System
     */
    @Test
    public void testStreamActorSystem() {
        // 验证 Stream 模块使用的是 TCS 的 Actor System
        assertEquals(tcsActorSystem, streamActorSystem, "Stream 模块应该使用 TCS 的 Actor System");
        
        // 验证 AkkaSystemEnvironment 中的 Actor System 是否正确设置
        assertEquals(tcsActorSystem, AkkaSystemEnvironment.getActorSystem(), "AkkaSystemEnvironment 应该使用 TCS 的 Actor System");
        
        // 验证 StreamRootActor 是否正确创建
        assertNotNull(streamRootActor, "StreamRootActor 应该被正确创建");
        
        // 验证 StreamRootActor 的路径
        assertEquals("akka://tcs/user/stream-root", streamRootActor.path().toString(), "StreamRootActor 的路径应该正确");
        
        log.info("Stream Actor System 集成测试通过！");
    }
}
