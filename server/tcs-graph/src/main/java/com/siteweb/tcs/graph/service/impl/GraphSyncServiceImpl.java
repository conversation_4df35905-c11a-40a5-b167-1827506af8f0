package com.siteweb.tcs.graph.service.impl;

import com.siteweb.tcs.graph.model.node.*;
import com.siteweb.tcs.graph.repository.*;
import com.siteweb.tcs.graph.service.GraphSyncService;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.dal.entity.Region;
import com.siteweb.tcs.hub.service.RegionService;
import com.siteweb.tcs.hub.service.impl.ForeignDeviceService;
import com.siteweb.tcs.hub.service.impl.ForeignGatewayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Implementation of the GraphSyncService interface
 */
@Service
@Slf4j
public class GraphSyncServiceImpl implements GraphSyncService {
    
    @Autowired
    private RegionService regionService;
    
    @Autowired
    private ForeignGatewayService foreignGatewayService;
    
    @Autowired
    private ForeignDeviceService foreignDeviceService;
    
    @Autowired
    private RegionNodeRepository regionNodeRepository;
    
    @Autowired
    private GatewayNodeRepository gatewayNodeRepository;
    
    @Autowired
    private DeviceNodeRepository deviceNodeRepository;
    
    @Autowired
    private ManufacturerNodeRepository manufacturerNodeRepository;
    
    @Autowired
    private DeviceTypeNodeRepository deviceTypeNodeRepository;
    
    @Value("${tcs.graph.sync.batch-size:1000}")
    private int batchSize;
    
    @Override
    @Transactional
    public Map<String, Object> syncAll() {
        log.info("Starting full synchronization of data to Neo4j");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<RegionNode> regions = syncRegions();
            List<GatewayNode> gateways = syncGateways();
            List<DeviceNode> devices = syncDevices();
            Map<String, Object> relationships = syncRelationships();
            
            result.put("success", true);
            result.put("timestamp", LocalDateTime.now());
            result.put("regions", regions.size());
            result.put("gateways", gateways.size());
            result.put("devices", devices.size());
            result.put("relationships", relationships);
            
            log.info("Full synchronization completed successfully: {} regions, {} gateways, {} devices",
                    regions.size(), gateways.size(), devices.size());
        } catch (Exception e) {
            log.error("Error during full synchronization", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public List<RegionNode> syncRegions() {
        log.info("Synchronizing regions to Neo4j");
        
        List<Region> regions = regionService.findAll();
        List<RegionNode> result = new ArrayList<>();
        
        for (Region region : regions) {
            try {
                RegionNode regionNode = regionNodeRepository.findBySourceId(region.getRegionId().toString())
                        .orElse(new RegionNode());
                
                regionNode.setSourceId(region.getRegionId().toString());
                regionNode.setName(region.getRegionName());
                regionNode.setDescription(region.getDescription());
                regionNode.setDisplayIndex(region.getDisplayIndex());
                regionNode.setResourceStructureId(region.getResourceStructureId());
                regionNode.setUpdatedAt(LocalDateTime.now());
                
                result.add(regionNodeRepository.save(regionNode));
                log.debug("Synchronized region: {}", region.getRegionName());
            } catch (Exception e) {
                log.error("Error synchronizing region {}: {}", region.getRegionId(), e.getMessage());
            }
        }
        
        log.info("Synchronized {} regions", result.size());
        return result;
    }
    
    @Override
    @Transactional
    public List<GatewayNode> syncGateways() {
        log.info("Synchronizing gateways to Neo4j");
        
        List<ForeignGateway> gateways = foreignGatewayService.list();
        List<GatewayNode> result = new ArrayList<>();
        
        for (ForeignGateway gateway : gateways) {
            try {
                GatewayNode gatewayNode = gatewayNodeRepository.findBySourceId(gateway.getForeignGatewayID())
                        .orElse(new GatewayNode());
                
                gatewayNode.setSourceId(gateway.getForeignGatewayID());
                gatewayNode.setName(gateway.getForeignGatewayID()); // Use ID as name if no name available
                gatewayNode.setPluginId(gateway.getPluginId());
                gatewayNode.setStationId(gateway.getStationId());
                gatewayNode.setMonitorUnitId(gateway.getMonitorUnitID());
                gatewayNode.setUpdatedAt(LocalDateTime.now());
                
                // Set manufacturer and model if available
                // This would typically come from additional metadata
                
                result.add(gatewayNodeRepository.save(gatewayNode));
                log.debug("Synchronized gateway: {}", gateway.getForeignGatewayID());
            } catch (Exception e) {
                log.error("Error synchronizing gateway {}: {}", gateway.getForeignGatewayID(), e.getMessage());
            }
        }
        
        log.info("Synchronized {} gateways", result.size());
        return result;
    }
    
    @Override
    @Transactional
    public List<DeviceNode> syncDevices() {
        log.info("Synchronizing devices to Neo4j");
        
        List<ForeignDevice> devices = foreignDeviceService.list();
        List<DeviceNode> result = new ArrayList<>();
        
        for (ForeignDevice device : devices) {
            try {
                DeviceNode deviceNode = deviceNodeRepository.findBySourceId(device.getForeignDeviceID())
                        .orElse(new DeviceNode());
                
                deviceNode.setSourceId(device.getForeignDeviceID());
                deviceNode.setName(device.getForeignDeviceID()); // Use ID as name if no name available
                deviceNode.setForeignDeviceId(device.getForeignDeviceID());
                deviceNode.setEquipmentId(device.getEquipmentId());
                deviceNode.setEquipmentTemplateId(device.getEquipmentTemplateId());
                deviceNode.setUpdatedAt(LocalDateTime.now());
                
                // Set device type, manufacturer and model if available
                // This would typically come from additional metadata
                
                result.add(deviceNodeRepository.save(deviceNode));
                log.debug("Synchronized device: {}", device.getForeignDeviceID());
            } catch (Exception e) {
                log.error("Error synchronizing device {}: {}", device.getForeignDeviceID(), e.getMessage());
            }
        }
        
        log.info("Synchronized {} devices", result.size());
        return result;
    }
    
    @Override
    @Transactional
    public Map<String, Object> syncRelationships() {
        log.info("Synchronizing relationships to Neo4j");
        
        Map<String, Object> result = new HashMap<>();
        int regionRelationships = 0;
        int gatewayRelationships = 0;
        int deviceRelationships = 0;
        
        try {
            // Sync region-to-region relationships
            List<Region> regions = regionService.findAll();
            Map<Long, RegionNode> regionNodeMap = regionNodeRepository.findAll().stream()
                    .collect(Collectors.toMap(
                            r -> Long.parseLong(r.getSourceId()),
                            r -> r
                    ));
            
            for (Region region : regions) {
                if (region.getParentId() != null && region.getParentId() > 0) {
                    RegionNode childNode = regionNodeMap.get(region.getRegionId());
                    RegionNode parentNode = regionNodeMap.get(region.getParentId());
                    
                    if (childNode != null && parentNode != null) {
                        if (!parentNode.getChildRegions().contains(childNode)) {
                            parentNode.getChildRegions().add(childNode);
                            regionNodeRepository.save(parentNode);
                            regionRelationships++;
                        }
                    }
                }
            }
            
            // Sync region-to-gateway relationships
            List<ForeignGateway> gateways = foreignGatewayService.list();
            Map<String, GatewayNode> gatewayNodeMap = gatewayNodeRepository.findAll().stream()
                    .collect(Collectors.toMap(
                            GatewayNode::getSourceId,
                            g -> g
                    ));
            
            for (ForeignGateway gateway : gateways) {
                if (gateway.getStationId() != null) {
                    // Find the region that contains this station
                    // This is a simplified approach - in a real implementation,
                    // you would need to determine the correct region based on your data model
                    Optional<Region> regionOpt = regions.stream()
                            .filter(r -> r.getResourceStructureId() != null && 
                                    r.getResourceStructureId().equals(gateway.getStationId()))
                            .findFirst();
                    
                    if (regionOpt.isPresent()) {
                        RegionNode regionNode = regionNodeMap.get(regionOpt.get().getRegionId());
                        GatewayNode gatewayNode = gatewayNodeMap.get(gateway.getForeignGatewayID());
                        
                        if (regionNode != null && gatewayNode != null) {
                            if (!regionNode.getGateways().contains(gatewayNode)) {
                                regionNode.getGateways().add(gatewayNode);
                                regionNodeRepository.save(regionNode);
                                gatewayRelationships++;
                            }
                        }
                    }
                }
            }
            
            // Sync gateway-to-device relationships
            List<ForeignDevice> devices = foreignDeviceService.list();
            Map<String, DeviceNode> deviceNodeMap = deviceNodeRepository.findAll().stream()
                    .collect(Collectors.toMap(
                            DeviceNode::getSourceId,
                            d -> d
                    ));
            
            for (ForeignDevice device : devices) {
                GatewayNode gatewayNode = gatewayNodeMap.get(device.getForeignGatewayID());
                DeviceNode deviceNode = deviceNodeMap.get(device.getForeignDeviceID());
                
                if (gatewayNode != null && deviceNode != null) {
                    if (!gatewayNode.getDevices().contains(deviceNode)) {
                        gatewayNode.getDevices().add(deviceNode);
                        gatewayNodeRepository.save(gatewayNode);
                        deviceRelationships++;
                    }
                }
            }
            
            result.put("success", true);
            result.put("regionRelationships", regionRelationships);
            result.put("gatewayRelationships", gatewayRelationships);
            result.put("deviceRelationships", deviceRelationships);
            
            log.info("Synchronized relationships: {} region, {} gateway, {} device",
                    regionRelationships, gatewayRelationships, deviceRelationships);
        } catch (Exception e) {
            log.error("Error synchronizing relationships", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> checkConsistency() {
        log.info("Checking consistency between relational database and Neo4j");
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            List<Region> regions = regionService.findAll();
            List<ForeignGateway> gateways = foreignGatewayService.list();
            List<ForeignDevice> devices = foreignDeviceService.list();
            
            long regionCount = regionNodeRepository.count();
            long gatewayCount = gatewayNodeRepository.count();
            long deviceCount = deviceNodeRepository.count();
            
            double regionConsistency = regions.isEmpty() ? 100 : (regionCount * 100.0 / regions.size());
            double gatewayConsistency = gateways.isEmpty() ? 100 : (gatewayCount * 100.0 / gateways.size());
            double deviceConsistency = devices.isEmpty() ? 100 : (deviceCount * 100.0 / devices.size());
            
            result.put("success", true);
            result.put("timestamp", LocalDateTime.now());
            result.put("relationalDb", Map.of(
                    "regions", regions.size(),
                    "gateways", gateways.size(),
                    "devices", devices.size()
            ));
            result.put("neo4j", Map.of(
                    "regions", regionCount,
                    "gateways", gatewayCount,
                    "devices", deviceCount
            ));
            result.put("consistency", Map.of(
                    "regions", regionConsistency,
                    "gateways", gatewayConsistency,
                    "devices", deviceConsistency,
                    "overall", (regionConsistency + gatewayConsistency + deviceConsistency) / 3
            ));
            
            log.info("Consistency check completed: regions {}%, gateways {}%, devices {}%",
                    String.format("%.2f", regionConsistency),
                    String.format("%.2f", gatewayConsistency),
                    String.format("%.2f", deviceConsistency));
        } catch (Exception e) {
            log.error("Error checking consistency", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }
    
    @Override
    @Transactional
    public RegionNode syncRegion(Long regionId) {
        log.info("Synchronizing region with ID: {}", regionId);
        
        try {
            Region region = regionService.findByRegionId(regionId.intValue());
            if (region == null) {
                log.error("Region not found with ID: {}", regionId);
                return null;
            }
            
            RegionNode regionNode = regionNodeRepository.findBySourceId(region.getRegionId().toString())
                    .orElse(new RegionNode());
            
            regionNode.setSourceId(region.getRegionId().toString());
            regionNode.setName(region.getRegionName());
            regionNode.setDescription(region.getDescription());
            regionNode.setDisplayIndex(region.getDisplayIndex());
            regionNode.setResourceStructureId(region.getResourceStructureId());
            regionNode.setUpdatedAt(LocalDateTime.now());
            
            RegionNode savedRegion = regionNodeRepository.save(regionNode);
            log.info("Synchronized region: {}", region.getRegionName());
            
            return savedRegion;
        } catch (Exception e) {
            log.error("Error synchronizing region {}: {}", regionId, e.getMessage());
            return null;
        }
    }
    
    @Override
    @Transactional
    public GatewayNode syncGateway(String gatewayId) {
        log.info("Synchronizing gateway with ID: {}", gatewayId);
        
        try {
            // Assuming there's a method to get a gateway by ID
            ForeignGateway gateway = foreignGatewayService.getOneForeignGateway(gatewayId, null);
            if (gateway == null) {
                log.error("Gateway not found with ID: {}", gatewayId);
                return null;
            }
            
            GatewayNode gatewayNode = gatewayNodeRepository.findBySourceId(gateway.getForeignGatewayID())
                    .orElse(new GatewayNode());
            
            gatewayNode.setSourceId(gateway.getForeignGatewayID());
            gatewayNode.setName(gateway.getForeignGatewayID()); // Use ID as name if no name available
            gatewayNode.setPluginId(gateway.getPluginId());
            gatewayNode.setStationId(gateway.getStationId());
            gatewayNode.setMonitorUnitId(gateway.getMonitorUnitID());
            gatewayNode.setUpdatedAt(LocalDateTime.now());
            
            GatewayNode savedGateway = gatewayNodeRepository.save(gatewayNode);
            log.info("Synchronized gateway: {}", gateway.getForeignGatewayID());
            
            return savedGateway;
        } catch (Exception e) {
            log.error("Error synchronizing gateway {}: {}", gatewayId, e.getMessage());
            return null;
        }
    }
    
    @Override
    @Transactional
    public DeviceNode syncDevice(String deviceId) {
        log.info("Synchronizing device with ID: {}", deviceId);
        
        try {
            // Assuming there's a method to get a device by ID
            ForeignDevice device = foreignDeviceService.getById(deviceId);
            if (device == null) {
                log.error("Device not found with ID: {}", deviceId);
                return null;
            }
            
            DeviceNode deviceNode = deviceNodeRepository.findBySourceId(device.getForeignDeviceID())
                    .orElse(new DeviceNode());
            
            deviceNode.setSourceId(device.getForeignDeviceID());
            deviceNode.setName(device.getForeignDeviceID()); // Use ID as name if no name available
            deviceNode.setForeignDeviceId(device.getForeignDeviceID());
            deviceNode.setEquipmentId(device.getEquipmentId());
            deviceNode.setEquipmentTemplateId(device.getEquipmentTemplateId());
            deviceNode.setUpdatedAt(LocalDateTime.now());
            
            DeviceNode savedDevice = deviceNodeRepository.save(deviceNode);
            log.info("Synchronized device: {}", device.getForeignDeviceID());
            
            return savedDevice;
        } catch (Exception e) {
            log.error("Error synchronizing device {}: {}", deviceId, e.getMessage());
            return null;
        }
    }
}
