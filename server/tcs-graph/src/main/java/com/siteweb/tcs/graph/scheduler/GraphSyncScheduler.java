package com.siteweb.tcs.graph.scheduler;

import com.siteweb.tcs.graph.service.GraphSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * Scheduler for graph synchronization
 */
@Component
@EnableScheduling
@Slf4j
public class GraphSyncScheduler {
    
    @Autowired
    private GraphSyncService graphSyncService;
    
    @Value("${tcs.graph.sync.auto-sync-enabled:true}")
    private boolean autoSyncEnabled;
    
    /**
     * Scheduled job to synchronize data from relational database to Neo4j
     */
    @Scheduled(initialDelayString = "${tcs.graph.sync.initial-delay-ms:60000}",
               fixedDelayString = "${tcs.graph.sync.interval-ms:3600000}")
    public void scheduledSync() {
        if (!autoSyncEnabled) {
            log.info("Automatic synchronization is disabled");
            return;
        }
        
        log.info("Starting scheduled synchronization at {}", 
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        try {
            Map<String, Object> result = graphSyncService.syncAll();
            
            if (Boolean.TRUE.equals(result.get("success"))) {
                log.info("Scheduled synchronization completed successfully: {} regions, {} gateways, {} devices",
                        result.get("regions"), result.get("gateways"), result.get("devices"));
            } else {
                log.error("Scheduled synchronization failed: {}", result.get("error"));
            }
        } catch (Exception e) {
            log.error("Error during scheduled synchronization", e);
        }
    }
    
    /**
     * Scheduled job to check consistency between relational database and Neo4j
     */
    @Scheduled(cron = "0 0 0 * * ?") // Run at midnight every day
    public void scheduledConsistencyCheck() {
        if (!autoSyncEnabled) {
            log.info("Automatic consistency check is disabled");
            return;
        }
        
        log.info("Starting scheduled consistency check at {}", 
                LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        
        try {
            Map<String, Object> result = graphSyncService.checkConsistency();
            
            if (Boolean.TRUE.equals(result.get("success"))) {
                Map<String, Object> consistency = (Map<String, Object>) result.get("consistency");
                double overall = (double) consistency.get("overall");
                
                log.info("Scheduled consistency check completed: overall consistency {}%",
                        String.format("%.2f", overall));
                
                if (overall < 95) {
                    log.warn("Consistency below threshold (95%). Triggering synchronization.");
                    graphSyncService.syncAll();
                }
            } else {
                log.error("Scheduled consistency check failed: {}", result.get("error"));
            }
        } catch (Exception e) {
            log.error("Error during scheduled consistency check", e);
        }
    }
}
