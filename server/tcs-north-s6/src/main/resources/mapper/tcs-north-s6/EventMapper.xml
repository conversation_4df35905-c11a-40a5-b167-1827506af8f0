<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.tcs.north.s6.dal.mapper.EventMapper">
<!--
    private Integer stationId;
    private Integer equipmentId;
    private Integer eventId;
    private Integer eventConditionId;
    private String sequenceId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Integer overturn;
    private String meanings;
    private Float eventValue;
    private Integer baseTypeId;
-->
    <select id="saveEventResponse" statementType="CALLABLE" resultType="int">
        CALL PNL_SaveEventResponse(
        #{stationId}, #{equipmentId}, #{eventId}, #{eventConditionId},
        #{sequenceId}, #{startTime}, #{endTime}, #{overturn},
        #{meanings}, #{eventValue}, #{baseTypeId},
        #{ret, mode=OUT, jdbcType=INTEGER})
    </select>
</mapper>

