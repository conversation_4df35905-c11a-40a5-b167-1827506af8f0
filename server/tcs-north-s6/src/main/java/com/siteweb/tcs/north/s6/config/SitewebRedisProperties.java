package com.siteweb.tcs.north.s6.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

/**
 * <AUTHOR> (2024-09-26)
 **/
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.data.redis")
public class SitewebRedisProperties {
    private int database = 0;

    private String host;

    /**
     * Login password of the redis server.
     */
    private String password;

    /**
     * Redis server port.
     */
    private int port;

    /**
     * Read timeout.
     */
    private Duration timeout;

    /**
     * Connection timeout.
     */
    private Duration connectTimeout;
}
