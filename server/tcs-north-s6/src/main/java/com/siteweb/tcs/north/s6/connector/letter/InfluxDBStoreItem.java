package com.siteweb.tcs.north.s6.connector.letter;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-08-28)
 **/
@Data
public class InfluxDBStoreItem {

    private String stationId;
    private String deviceId;
    private String signalId;
    private String baseTypeId;
    private String signalType;
    private Double value;
    private LocalDateTime recordTime;

    public String realTimeKey(){
        return deviceId + "." + signalId;
    }

}
