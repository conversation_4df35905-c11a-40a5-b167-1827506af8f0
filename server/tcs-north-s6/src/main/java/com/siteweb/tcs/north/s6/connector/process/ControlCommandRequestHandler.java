package com.siteweb.tcs.north.s6.connector.process;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.door.*;
import com.siteweb.tcs.hub.domain.letter.ControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.DoorControlRequestParam;
import com.siteweb.tcs.hub.domain.letter.DynamicValue;
import com.siteweb.tcs.hub.domain.letter.EquipmentControlCommandRequest;
import com.siteweb.tcs.hub.domain.letter.enums.CmdDeviceCategoryEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ControlModeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.ControlTypeEnum;
import com.siteweb.tcs.hub.domain.letter.enums.DynamicValueType;
import com.siteweb.tcs.hub.service.impl.ForeignGatewayService;
import com.siteweb.tcs.north.s6.connector.protocol.DistributeControl;
import com.siteweb.tcs.north.s6.connector.protocol.PubSubMessage;
import com.siteweb.tcs.north.s6.dal.mapper.ActiveControlMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorSelection;
import org.apache.pekko.actor.Props;
import org.siteweb.config.client.provider.*;
import org.siteweb.config.common.dto.ControlConfigItem;
import org.siteweb.config.common.entity.TblEquipment;
import org.siteweb.config.common.entity.TblFaceData;
import org.siteweb.config.common.entity.TblFingerPrint;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
public class ControlCommandRequestHandler extends AbstractActor implements MessageListener {

    private final StringRedisTemplate redisTemplate;
    private final ObjectMapper objectMapper;
    private final HashMap<Integer, ActorSelection> deviceStateStores = new HashMap<>();
    private final String CHANNEL = "ControlRequest";

    private final EquipmentProvider equipmentProvider;
    private final CommandProvider commandProvider;

    private final DoorProvider doorProvider;

    private final ActiveControlMapper activeControlMapper;


    private final CardProvider cardProvider;
    private final FingerPrintProvider fingerPrintProvider;
    private final FaceDataProvider faceDataProvider;

    private final ForeignGatewayService foreignGatewayService;


    public static Props props() {
        return Props.create(ControlCommandRequestHandler.class);
    }

    public ControlCommandRequestHandler() {
        this.objectMapper = PluginScope.getBean(ObjectMapper.class);
        this.commandProvider = PluginScope.getBean(CommandProvider.class);
        this.equipmentProvider = PluginScope.getBean(EquipmentProvider.class);
        this.redisTemplate = (StringRedisTemplate) PluginScope.getBean("SitewebRedisTemplate");
        this.faceDataProvider = PluginScope.getBean(FaceDataProvider.class);
        this.fingerPrintProvider = PluginScope.getBean(FingerPrintProvider.class);
        this.doorProvider = PluginScope.getBean(DoorProvider.class);
        this.cardProvider = PluginScope.getBean(CardProvider.class);
        this.activeControlMapper = PluginScope.getBean(ActiveControlMapper.class);
        this.foreignGatewayService = PluginScope.getBean(ForeignGatewayService.class);
    }


    @Override
    public void preStart() throws Exception {
        RedisConnection connection = Objects.requireNonNull(redisTemplate.getConnectionFactory()).getConnection();
        connection.subscribe(this, CHANNEL.getBytes());
        log.info("Subscribe Redis Message：Siteweb [ControlRequest ]");
        super.preStart();
    }


    @Override
    public void postStop() throws Exception {
        RedisConnection connection = Objects.requireNonNull(redisTemplate.getConnectionFactory()).getConnection();
        var subscription = connection.getSubscription();
        if (subscription != null) {
            subscription.unsubscribe(CHANNEL.getBytes());
        }
        super.postStop();
    }


    @Override
    public AbstractActor.Receive createReceive() {
        return receiveBuilder()
                .build();
    }


    @Override
    public void onMessage(Message message, byte[] pattern) {

        String msgBody = null;
        try {
            // 忽略 'CTRL:PULSE' 消息
            msgBody = new String(message.getBody());
            log.trace("收到REDIS订阅消息： {}", msgBody);
            if ("CTRL:PULSE".equals(msgBody)) return;
//            msgBody = "{\"HostId\":0,\"DesHostId\":755010007,\"MessageType\":0,\"MessageBody\":\"{\\\"StationId\\\":755000001,\\\"HostId\\\":755010007,\\\"EquipmentId\\\":755000034,\\\"ControlId\\\":755000010,\\\"BaseTypeId\\\":0,\\\"UserId\\\":-1,\\\"SerialNo\\\":18,\\\"ControlType\\\":0,\\\"Priority\\\":1,\\\"ParameterValues\\\":\\\"1,23\\\",\\\"StartTime\\\":\\\"2024-09-27T13:06:24\\\"}\"}";
            //
            var pubSubMessage = objectMapper.readValue(msgBody, PubSubMessage.class);
            log.trace("收到控制命令：HostId: {}, DesHostId: {}, MessageType: {}, Body: {}",
                    pubSubMessage.getHostId(),
                    pubSubMessage.getDesHostId(),
                    pubSubMessage.getMessageType(),
                    pubSubMessage.getMessageBody()
            );

            /* pubSubMessage 例子
            {

                "HostId": 451000011,
                "DesHostId": 451000011,
                "messageType": 50,
                "messageBody": {
                    "stationId": -451,
                    "hostId": 451000011,
                    "equipmentId": 451000002,
                    "controlId": 510000340,
                    "baseTypeId": 1004303001,
                    "userId": -1,
                    "serialNo": 1,
                    "controlType": 1,
                    "priority": 1,
                    "parameterValues": "0,0.0",
                    "startTime": "2024-08-10T11:08:25"
                }
            }
            */
            var distributeControl = objectMapper.readValue(pubSubMessage.getMessageBody(), DistributeControl.class);
            log.trace("收到分发控制：HostId: {}, EquipmentId: {}, ControlId: {}, Parameter: {}",
                    distributeControl.getHostId(),
                    distributeControl.getEquipmentId(),
                    distributeControl.getControlId(),
                    distributeControl.getParameterValues()
            );
            this.processControl(pubSubMessage.getDesHostId(), distributeControl);
        } catch (Exception e) {
            log.error("无法识别的控制命令消息:{}", msgBody, e);
        }
    }


    private void processControl(int hostId, DistributeControl distributeControl) {
        if (!this.foreignGatewayService.hasForeignGateway(String.valueOf(hostId))) {
            log.error("控制命令GatewayId,MonitorUnitId[{}]不属于当前TCS，已跳过。", hostId);
            return;
        }
        var eq = equipmentProvider.findByID(distributeControl.getEquipmentId());
        ControlConfigItem controlItem = commandProvider.getControlForEquipment(distributeControl.getStationId(), distributeControl.getEquipmentId(), distributeControl.getControlId());
        Integer controlCategory = controlItem.getControlCategory();
        DynamicValueType dynamicValueType = null;
        ControlCommandRequest commandRequest = new ControlCommandRequest();
        commandRequest.setStationId(distributeControl.getStationId());
        commandRequest.setControlCategory(controlCategory);
        commandRequest.setControlId(distributeControl.getControlId());
        commandRequest.setEquipmentId(distributeControl.getEquipmentId());
        commandRequest.setMonitorUnitId(hostId);
        commandRequest.setStartTime(distributeControl.getStartTime());
        commandRequest.setSequenceNo(distributeControl.getSerialNo().toString());
        commandRequest.setUserId(distributeControl.getUserId());
        commandRequest.setPriority(distributeControl.getPriority());
        commandRequest.setControlType(ControlTypeEnum.fromInt(controlItem.getCommandType()));
        commandRequest.setControlMode(ControlModeEnum.Manual);
        DynamicValue dynamicValue = null;
        if (eq.getEquipmentCategory() != null && eq.getEquipmentCategory().equals(82)) {
            log.trace("门禁设备控制");
            dynamicValue = parseDoorParameter(eq, controlItem, distributeControl);
        } else {
            log.trace("普通设备控制");
            dynamicValue = DynamicValue.parse(distributeControl.getParameterValues(), dynamicValueType);
        }
        commandRequest.setParameter(dynamicValue);
        // send to .....
        EquipmentControlCommandRequest equipmentControlCommandRequest = new EquipmentControlCommandRequest(
                commandRequest.getMonitorUnitId(),
                commandRequest.getEquipmentId(),
                eq.getEquipmentCategory() == 82 ? CmdDeviceCategoryEnum.DOOR_ACCESS : CmdDeviceCategoryEnum.GENERAL,
                List.of(commandRequest));
//        DynamicValueType valueType = commandRequest.getParameter().getValueType();
        ActorSelection stateStore = getEquipmentControlCommandStateStore(commandRequest);
        if (this.activeControlMapper.changePhase2(distributeControl.getSerialNo())) {
            log.trace("ActiveControl reTry + 1， change to phase 2");
            log.trace("路由控制命令至{}", stateStore.path());
            stateStore.tell(equipmentControlCommandRequest, self());
        }else{
            log.trace("ActiveControl已被处理（reTry = 2），跳过。");
        }
    }


    private DynamicValue parseDoorParameter(TblEquipment eq, ControlConfigItem controlItem, DistributeControl distributeControl) {
        var cardInfo = new CardInfo();
        var parameterValues = distributeControl.getParameterValues();
        var doors = this.doorProvider.getDoor(eq.getEquipmentId());
        if (doors == null || doors.isEmpty()) {
            log.error("错误的控制");
            return null;
        }
        var door = doors.get(0);
        DoorControlRequestParam requestParam = new DoorControlRequestParam();
        requestParam.setDoorNo(door.getDoorNo());
        var doorInfo = new DoorInfo();
        doorInfo.setDoorNo(door.getDoorNo());
        doorInfo.setDoorId(door.getDoorId());
        doorInfo.setDoorName(door.getDoorName());
        doorInfo.setDoorControlId(door.getDoorControlId());
        doorInfo.setCategory(door.getCategory());
        doorInfo.setStationId(eq.getStationId());

        var userInfo = new UserInfo();
        userInfo.setUserId(distributeControl.getUserId());
        userInfo.setUserName("xxx");


        var timeGroupInfo = new TimeGroupInfo();


        Integer controlCategory = controlItem.getControlCategory();
        // 门禁的控制
        if (controlCategory == 33 || controlCategory == 51) {
            // 51和52是自产门禁使用，51是读头添加人脸; 52是读头删除人脸,此处不需要转换   33判断是人脸数据下发
            FaceInfo info = getHumFaceDATA(parameterValues);
            requestParam.setFaceInfo(info);
        }
        if (controlCategory == 31 || controlCategory == 40) {
            // controlCategory = 31是之前海康指纹，40 是本次新增的纽贝尔指纹
            FingerInfo info = getFingerPrint(parameterValues, controlCategory);
            requestParam.setFingerInfo(info);
        }
        if (controlCategory == 2) {
            // 开关门
            var segments = parameterValues.split(",");
            doorInfo.setPassword(segments[0]);
        }

        if (controlCategory == 12) {
            // 添加卡
            var segments = parameterValues.split("[,|+]");
            doorInfo.setPassword(segments[0]);
            var card = this.cardProvider.findCardByCode(segments[2]);
            cardInfo.setCardId(card.getCardId());
            cardInfo.setCardName(card.getCardName());
            cardInfo.setUserId(card.getUserId());
            cardInfo.setPassword(segments[4]);
            cardInfo.setStartTime(card.getStartTime());
            cardInfo.setHexCardCode(card.getCardCode());
            var octal = Long.parseLong(card.getCardCode(), 16);
            cardInfo.setOctalCardCode(String.valueOf(octal));
            cardInfo.setEndTime(card.getEndTime());
        }


        if (controlCategory == 13) {
            // 删除卡
            var segments = parameterValues.split("[,|+]");
            doorInfo.setPassword(segments[0]);
            var card = this.cardProvider.findCardByCode(segments[2]);
            cardInfo.setCardId(card.getCardId());
            cardInfo.setCardName(card.getCardName());
            cardInfo.setUserId(card.getUserId());
            cardInfo.setStartTime(card.getStartTime());
            cardInfo.setHexCardCode(card.getCardCode());
            var octal = Long.parseLong(card.getCardCode(), 16);
            cardInfo.setOctalCardCode(String.valueOf(octal));
            cardInfo.setEndTime(card.getEndTime());
        }


        requestParam.setUserInfo(userInfo);
        requestParam.setDoorInfo(doorInfo);
        requestParam.setCardInfo(cardInfo);
        requestParam.setTimeGroupInfo(timeGroupInfo);

        // Action
        // 门号
        // 准入时间段
        // 开门方式

        // 卡号
        // 门密码
        //

//    31	2	开关门控制	Switch Door Access
//    31	12	增加门禁卡	Add Door Access Card
//    31	13	删除门禁卡	Delete Door Access Card
//    31	14	修改门禁卡设置	Modify Door Access Card Setting
//    31	15	设置星期准进时间段 	Set Week Ingress Time Period
//    31	16	修改验证控制密码	Modify Control Privilege Password
//    31	17	删除所有门禁卡	Delete All Door Access Card
//    31	20	开门超时时间	Door Open Timeout
//    31	21	刷卡进门密码工作方式	Swipe Card Entry Password Work Mode
//    31	22	设置时间	Set Time
//    31	23	非法开门告警结束确定命令	Illegal Door Open Event End Confirm Command


        DynamicValue value = new DynamicValue();
        value.setObjectValue(requestParam);
        value.setValueType(DynamicValueType.DOOR);
        return value;

    }


    private FingerInfo getFingerPrint(String value, Integer controlCategory) {
        FingerInfo result = new FingerInfo();
        int fpLastIndex = value.lastIndexOf(',');
        String strFPId = value.substring(fpLastIndex + 1, value.length() - fpLastIndex - 1);// 指纹控制命令参数的最后一个逗号后的字符串就是bioId(指纹ID)
        if (controlCategory == 31)//海康指纹
        {
            var list = fingerPrintProvider.findListById(strFPId);
            var dicFingerPrint = list.stream().collect(Collectors.toMap(TblFingerPrint::getFingerPrintId, v -> v));

//            Map<Integer, byte[]> dicFingerPrint = fingerPrintProvider.findListById(strFPId);
            String fpPrePara = value.substring(0, fpLastIndex + 1); // 最后一个逗号之前（含逗号）的参数字符串
            byte[] fpBytePrePara = fpPrePara.getBytes(StandardCharsets.UTF_8);
            int fpCount = dicFingerPrint.size(); // 指纹个数
            int indexFP = 0;
            Byte[] fpData = new Byte[fpBytePrePara.length + 1];
            // 拷贝参数字符串，含逗号
            System.arraycopy(fpBytePrePara, 0, fpData, indexFP, fpBytePrePara.length);
            indexFP += fpBytePrePara.length;
            fpData[indexFP++] = (byte) fpCount; // 指纹总数

            for (Map.Entry<Integer, TblFingerPrint> entry : dicFingerPrint.entrySet()) {
                int fpNo = entry.getKey();
                byte[] fingerprint = entry.getValue().getFingerPrintData();
                int fpLength = fingerprint.length;
                short usFpLength = (short) fpLength;
                // 调整 fpData 数组大小
                fpData = Arrays.copyOf(fpData, indexFP + 1 + 2 + fpLength);
                // 存放指纹编号
                fpData[indexFP++] = (byte) fpNo;
                // 存放指纹长度
                byte[] bytesUsFpLength = new byte[]{(byte) (usFpLength & 0xff), (byte) ((usFpLength >> 8) & 0xff)};
                System.arraycopy(bytesUsFpLength, 0, fpData, indexFP, 2);
                indexFP += 2;
                // 存放指纹数据
                System.arraycopy(fingerprint, 0, fpData, indexFP, fpLength);
                indexFP += fpLength;
            }
            result.setFingerPrintId(Integer.parseInt(strFPId));
            result.setFingerPrintNo(0);
            result.setFingerPrintData(fpData);

        } else if (controlCategory == 40)//纽贝尔指纹
        {//下发指纹  格式：18,权限密码,指纹编号,指纹长度,指纹
            //指纹编号的最后一位是 手指号
            String[] arrFValues = value.split(",");
            String strFPNo = arrFValues[1].substring(arrFValues[1].length() - 1); // 手指号，指纹编号的最后一位
            var list = fingerPrintProvider.findListById(strFPId, strFPNo);
            var dicFingerPrint = list.stream().collect(Collectors.toMap(TblFingerPrint::getFingerPrintId, v -> v));

            String fpPrePara = value.substring(0, fpLastIndex + 1); // 最后一个逗号之前（含逗号）的参数字符串
            byte[] fpBytePrePara = fpPrePara.getBytes(StandardCharsets.UTF_8);
            int indexFP = 0;
            Byte[] fpData = new Byte[fpBytePrePara.length];
            // 拷贝参数字符串，含逗号
            System.arraycopy(fpBytePrePara, 0, fpData, indexFP, fpBytePrePara.length);
            indexFP += fpBytePrePara.length;
            if (!dicFingerPrint.isEmpty()) {
                Map.Entry<Integer, TblFingerPrint> firstFP = dicFingerPrint.entrySet().iterator().next();
                byte[] fingerprint = firstFP.getValue().getFingerPrintData();
                int fpLength = fingerprint.length;
                // 调整 fpData 数组大小
                fpData = Arrays.copyOf(fpData, indexFP + fpLength);
                // 拷贝指纹数据
                System.arraycopy(fingerprint, 0, fpData, indexFP, fpLength);
                indexFP += fpLength;
            } else {
                log.info(String.format("未能从数据库中获取到指纹数据: fringerPrintId=%s, fringerPrintNo=%s", strFPId, strFPNo));
            }
            result.setFingerPrintId(Integer.parseInt(strFPId));
            result.setFingerPrintNo(Integer.parseInt(strFPNo));
            result.setFingerPrintData(fpData);
        }

        return null;
    }


    private FaceInfo getHumFaceDATA(String value) {
        //m_logger.Info("----------准备下发生物数据相关的控制命令--------");
        FaceInfo result = new FaceInfo();
        int lastIndex = value.lastIndexOf(',');
        String strBioId = value.substring(lastIndex + 1, value.length() - lastIndex - 1);
        // paras[pLen - 1];
        // 人脸(或指纹)控制命令参数的最后一个逗号后的字符串就是bioId
        log.info("----------------生物数据ID = {}", strBioId);
        log.info("----------------------从数据库获取生物数据----begin");
        TblFaceData faceData = faceDataProvider.findById(strBioId);
        byte[] bioData = faceData.getFaceData();
        log.info("----------------------从数据库获取生物数据----end");
        String strPrePara = value.substring(0, lastIndex + 1);//最后一个逗号之前（含逗号）参数字符串
        byte[] bytePrePara = strPrePara.getBytes(StandardCharsets.UTF_8);
        Byte[] byteParaValue = new Byte[bytePrePara.length + bioData.length];
        System.arraycopy(bytePrePara, 0, byteParaValue, 0, bytePrePara.length);
        System.arraycopy(bioData, 0, byteParaValue, bytePrePara.length, bioData.length);
        result.setFaceId(Integer.parseInt(strBioId));
        result.setFaceData(byteParaValue);
        return result;
    }


    private ActorSelection getEquipmentControlCommandStateStore(ControlCommandRequest commandRequest) {
        if (this.deviceStateStores.containsKey(commandRequest.getEquipmentId())) {
            return this.deviceStateStores.get(commandRequest.getEquipmentId());
        } else {
            ActorSelection stateStore = getContext().actorSelection(ActorPathBuilder.create().withHubRoot().append("deviceControlCommandKeeper")
                    .append("equipmentControlCommandStateStore").appendIds(commandRequest.getMonitorUnitId(), commandRequest.getEquipmentId()).build());
            this.deviceStateStores.put(commandRequest.getEquipmentId(), stateStore);
            return stateStore;
        }
    }


}

