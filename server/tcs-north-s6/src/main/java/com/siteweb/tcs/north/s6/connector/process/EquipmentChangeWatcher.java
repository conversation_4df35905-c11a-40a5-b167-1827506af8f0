package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.hub.domain.letter.EquipmentChange;
import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.connector.letter.DelRedisItemAction;
import com.siteweb.tcs.north.s6.connector.letter.SetRedisItemAction;
import com.siteweb.tcs.north.s6.dal.provider.EquipmentProvider;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

@Slf4j
public class EquipmentChangeWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    private final ActorRef redisSink;

    private final Map<Integer,EquipmentChange> equipmentChangeMap = new HashMap<>();

//    private final Scheduler scheduler ;
    private EquipmentProvider equipmentProvider = ConnectorDataHolder.getContext().getBean(EquipmentProvider.class);

    private EquipmentChangeWatcher(ActorRef redisSink){
        this.redisSink = redisSink;
        //定时任务，S6不用
//        this.scheduler = ActorRegistry.getActorSystem().scheduler();
//        scheduler.scheduleAtFixedRate(Duration.ofSeconds(30),Duration.ofSeconds(60),()->reportHeartbeat(),getContext().getDispatcher());
    }
    
    public static Props props(ActorRef redisSink)
    {
        return Props.create(EquipmentChangeWatcher.class, ()->new EquipmentChangeWatcher(redisSink));
    }

    public AbstractActor.Receive createReceive()
    {
        return receiveBuilder()
                .match(EquipmentChange.class , this::onEquipmentChange)
                .build();
    }

    private void onEquipmentChange(EquipmentChange equipmentChange) {
        /**
         * 1.判断设备状态是在线还是离线
         * 2.在线判断当前缓存有没有，有证明重复报了，更新时间，打印日志；没有就加入一条
         * 3.离线判断删除本地缓存
         */
        if(ObjectUtil.isEmpty(equipmentChange)) return;
        Integer equipmentId = equipmentChange.getEquipmentId();
        boolean isExist = equipmentChangeMap.containsKey(equipmentId);
        EquipmentChange existChange = equipmentChangeMap.get(equipmentId);
        switch (EnumDeviceConnectState.fromCode(equipmentChange.getEquipmentState())){
            case OFFLINE:
                if(isExist && ObjectUtil.notEqual(existChange.getEquipmentState(),equipmentChange.getEquipmentState())){
                    existChange.setChange(true);
                    existChange.setEquipmentState(EnumDeviceConnectState.OFFLINE.getCode());
                    log.info("[Equipment Connect State] {} connect state change to {}",equipmentChange,EnumDeviceConnectState.OFFLINE.getDescription());
                }
                break;
            case ONLINE:
                if(!isExist || ObjectUtil.notEqual(existChange.getEquipmentState(),equipmentChange.getEquipmentState())){
                    equipmentChange.setChange(true);
                    equipmentChangeMap.put(equipmentChange.getEquipmentId(),equipmentChange);
                    log.info("[Equipment Connect State] {} connect state change to {}",equipmentChange,EnumDeviceConnectState.ONLINE.getDescription());
                }
                break;
            default:
                break;
        }
    }

    //目前一分钟一次
    private void reportHeartbeat(){
        if(equipmentChangeMap.isEmpty()) return;

        //redis
        SetRedisItemAction setRedisItemAction = new SetRedisItemAction();
        DelRedisItemAction delRedisItemAction = new DelRedisItemAction();
        //mysql
        List<Integer> onLineEquipmentIdList = new ArrayList<>();
        List<Integer> offLineEquipmentIdList = new ArrayList<>();
        //筛选出离线且变化的
        List<EquipmentChange> offLineList = equipmentChangeMap.values().stream()
                .filter(e -> e.isChange() && ObjectUtil.equal(e.getEquipmentState(), EnumDeviceConnectState.OFFLINE.getCode()))
                .toList();
        //筛选出在线且变化的
        List<EquipmentChange> onLineList = equipmentChangeMap.values().stream()
                .filter(e -> e.isChange() && ObjectUtil.equal(e.getEquipmentState(), EnumDeviceConnectState.ONLINE.getCode()))
                .toList();
        //离线更新
        if(CollectionUtil.isNotEmpty(offLineList)){
            offLineList.forEach(e -> {
                delRedisItemAction.addItem(e.getRedisKey());
                offLineEquipmentIdList.add(e.getEquipmentId());
                e.setChange(false);
            });
            redisSink.tell(delRedisItemAction,getSender());
            equipmentProvider.updateConnectState(offLineEquipmentIdList,EnumDeviceConnectState.OFFLINE.getCode());
        }
        //在线更新
        if(CollectionUtil.isNotEmpty(onLineList)){
            onLineList.forEach(e -> {
                setRedisItemAction.addItem(e.getRedisKey(),e.getRedisValue());
                onLineEquipmentIdList.add(e.getEquipmentId());
                e.setChange(false);
            });
            redisSink.tell(setRedisItemAction,getSender());
            equipmentProvider.updateConnectState(onLineEquipmentIdList,EnumDeviceConnectState.ONLINE.getCode());
        }

    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }
}

