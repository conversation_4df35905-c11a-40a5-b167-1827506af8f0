package com.siteweb.tcs.north.s6.dal.provider;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName: EquipmentProvider
 * @descriptions: SiteWeb设备相关操作
 * @author: xsx
 * @date: 2024/8/15 12:59
 **/
public interface EquipmentProvider {
    void updateConnectState(Integer equipmentId,Integer connectState);
    void updateConnectState(List<Integer> equipmentId, Integer connectState);
    void updateConnectStateByMonitorUnitId(Collection<Integer> monitorUnitIdList, Integer fromConnectState, Integer toConnectState);
}
