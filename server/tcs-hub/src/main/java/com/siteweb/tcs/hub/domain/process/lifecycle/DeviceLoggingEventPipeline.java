package com.siteweb.tcs.hub.domain.process.lifecycle;

import com.siteweb.tcs.common.util.ActorPathBuilder;
import com.siteweb.tcs.hub.dal.entity.ForeignDevice;
import com.siteweb.tcs.hub.domain.process.DeviceLoggingEventAdapter;
import com.siteweb.tcs.hub.domain.process.EquipmentLoggingEventSpout;
import com.siteweb.tcs.hub.domain.process.EquipmentLoggingEventStore;
import org.apache.pekko.actor.ActorContext;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;

/**
 * 设备日志事件管道
 * 负责处理设备日志事件的数据流
 */
public class DeviceLoggingEventPipeline extends DataPipeline {
    private final ForeignDevice device;
    
    /**
     * 构造函数
     * @param context Actor上下文
     * @param device 外部设备实体
     */
    public DeviceLoggingEventPipeline(ActorContext context, ForeignDevice device, ActorRef pipelinePublisher) {
        super(context, pipelinePublisher);
        this.device = device;
    }
    
    @Override
    public void create() {
        // 创建日志事件发送Actor
        ActorRef spoutActor = getContext().actorOf(
                Props.create(EquipmentLoggingEventSpout.class, device, getPipelinePublisher()),
                "EquipmentLoggingEventSpout"
        );
        setSpoutActor(spoutActor);
        
        // 创建日志事件存储Actor
        ActorRef storeActor = getContext().actorOf(
                EquipmentLoggingEventStore.props(spoutActor),
                "EquipmentLoggingEventStore"
        );
        setStoreActor(storeActor);
        
        // 创建日志事件适配器Actor
        ActorRef adapterActor = getContext().actorOf(
                DeviceLoggingEventAdapter.props(device, storeActor),
                "DeviceLoggingEventAdapter"
        );
        setAdapterActor(adapterActor);
    }
}

