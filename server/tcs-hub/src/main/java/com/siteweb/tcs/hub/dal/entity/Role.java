package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("tcs_role")
public class Role {

    @TableId(value = "roleId", type = IdType.AUTO)
    private Integer roleId;

    private String roleName;

    private String description;

    //@TableField(exist = false)
    //List<RolePermissionMap> rolePermissionMapList;
}
