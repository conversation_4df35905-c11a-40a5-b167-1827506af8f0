package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@TableName("tcs_menu_item")
public class MenuItem {
    @TableId(value = "menuItemId", type = IdType.INPUT)
    private Integer menuItemId;
    /**
     * 插件ID
     */
    private String pluginId;
    /**
     * 菜单名称
     */
    private String menuItemName;
    /**
     * 父节点ID
     */
    private Integer parentMenuItemId;
    /**
     * 路由
     */
    private String path;
    /**
     * 图标
     */
    private String icon;
    /**
     * 是否选择
     */
    private Boolean selected;
    /**
     * 是否展开
     */
    private Boolean expanded;
    /**
     * 布局位置（控制顺序）
     */
    private Integer layoutPosition;
    /**
     * 是否系统配置
     */
    private Boolean isSystemConfig;
    /**
     * 是否外链
     */
    private Boolean isExternalWeb;
    /**
     * 描述
     */
    private String description;

    @TableField(exist = false)
    private List<MenuItem> children;
    public List<MenuItem> getChildren() {
        if(children == null){
            children = new ArrayList<>();
        }
        return children;
    }
}
