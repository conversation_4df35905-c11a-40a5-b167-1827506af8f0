package com.siteweb.tcs.hub.security;

import com.siteweb.tcs.hub.dal.dto.AccountDTO;
import com.siteweb.tcs.hub.service.AccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AccountExpiredException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.LockedException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("tokenUserDetailService")
public class TokenUserDetailsService implements UserDetailsService {

    @Autowired
    private AccountService accountService;

    @Override
    public final TokenUser loadUserByUsername(String username) {
        final List<AccountDTO> users = accountService.findByLoginId(username);
        if (users == null || users.isEmpty()) {
            return null;
        }
        AccountDTO user = users.get(0);
        if (user.isLocked()) {
            throw new LockedException("User account is locked");
        } else if (!user.isEnable()) {
            throw new DisabledException("User is disabled");
        } else if (!isValidDate(user)) {
            throw new AccountExpiredException("User account has expired");
        } else if (!isPasswordValidDate(user)) {
            throw new AccountExpiredException("User password is expired");
        }
        return new TokenUser(user);
    }

    /** 判断密码是否在有效期内 */
    private boolean isPasswordValidDate(AccountDTO user) {
        if (user.getPasswordValidTime() == null) {
            return true;
        }
        return user.getPasswordValidTime().after(new Date());
    }

    private boolean isValidDate(AccountDTO user) {
        if (user.getValidTime() == null) {
            return true;
        }
        return user.getValidTime().after(new Date());
    }

}
