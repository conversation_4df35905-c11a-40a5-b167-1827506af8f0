package com.siteweb.tcs.hub.domain.letter;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class LiveMonitorUnitState {
    private int monitorUnitID;
    private String ip;
    private String port;
    private int connectState;
    private String firmWareVersion;
    private String softWareVersion;
    private String vendor;
    private String modal;
    private LocalDateTime timeStamp;

    public void update(MonitorUnitChange monitorUnitChange) {
        this.ip = monitorUnitChange.getIP();
        this.port = monitorUnitChange.getPort();
        this.connectState = monitorUnitChange.getConnectState();
        this.firmWareVersion = monitorUnitChange.getFirmWareVersion();
        this.softWareVersion = monitorUnitChange.getSoftWareVersion();
        this.vendor = monitorUnitChange.getVendor();
        this.modal = monitorUnitChange.getModal();
        this.timeStamp = monitorUnitChange.getTimestamp();
    }
}
