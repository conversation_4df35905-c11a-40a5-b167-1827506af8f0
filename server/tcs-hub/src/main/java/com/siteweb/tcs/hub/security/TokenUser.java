package com.siteweb.tcs.hub.security;

import com.siteweb.tcs.hub.dal.dto.AccountDTO;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.User;

public class TokenUser extends User {

    private AccountDTO user;

    public TokenUser(AccountDTO user) {
        super(user.getLoginId(), user.getPassword() != null ? user.getPassword() : "", AuthorityUtils.createAuthorityList(user.getRoleIds() != null ? user.getRoleIds() : ""));
        this.user = user;
    }

    public AccountDTO getUser() {
        return user;
    }

    public String getRole() {
        return user.getRoleIds();
    }

    public int getUserId() {
        return user.getUserId();
    }


    @Override
    public boolean equals(Object obj) {
        return super.equals(obj);
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }
}
