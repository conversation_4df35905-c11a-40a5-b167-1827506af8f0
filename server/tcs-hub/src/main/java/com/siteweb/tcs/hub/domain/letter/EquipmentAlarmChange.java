package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.common.o11y.WindowLogItem;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
public class EquipmentAlarmChange extends BaseEquipmentDTO implements WindowLogItem {

    private Integer eventId;
    private Integer eventConditionId;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private String triggerValue;
    private Integer alarmLevel;
    private String Meanings;
    private String serialNo;

    @Override
    public String getWindowLogString() {
        return "EquipmentAlarmChange{" +
                "monitorUnitId=" + monitorUnitId +
                ", equipmentId=" + equipmentId +
                ", eventId=" + eventId +
                ", eventConditionId=" + eventConditionId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", triggerValue='" + triggerValue + '\'' +
                ", alarmLevel=" + alarmLevel +
                ", Meanings='" + Meanings + '\'' +
                ", serialNo='" + serialNo + '\'' +
                '}';
    }

    public String getUniqueId() {
        return String.format("%d_%d_%d_%d", monitorUnitId, equipmentId, eventId, eventConditionId);
    }
}
