package com.siteweb.tcs.hub.domain.letter;

import com.siteweb.tcs.hub.domain.letter.enums.LoggingEventEnum;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

/**
 * @ClassName: EquipmentLoggingEvent
 * @descriptions: 设备日志事件
 * @author: xsx
 * @date: 2024/9/25 13:49
 **/
@Data
public class EquipmentLoggingEvent {
    private Integer stationId;
    private Integer monitorUnitId;
    private Integer equipmentId;
    private LocalTime recordTime;
    private LoggingEventEnum loggingEventEnum;
    private List<LoggingEvent> loggingEvent;
}
