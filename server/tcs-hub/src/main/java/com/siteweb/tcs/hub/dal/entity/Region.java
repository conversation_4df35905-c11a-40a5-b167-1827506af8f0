package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


@Data
@TableName("tcs_regions")
public class Region {
    @TableId(value = "regionId", type = IdType.AUTO)
    private Long regionId;
    private Long parentId;
    private String regionName;
    private String description;
    private Integer displayIndex;
    private Integer resourceStructureId;
    @TableField(exist = false)
    private Integer parentResourceStructureId;
}