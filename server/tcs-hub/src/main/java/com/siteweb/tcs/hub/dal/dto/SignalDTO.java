package com.siteweb.tcs.hub.dal.dto;

import com.siteweb.tcs.hub.dal.entity.ForeignSignal;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import com.siteweb.tcs.siteweb.entity.SignalMeanings;
import com.siteweb.tcs.siteweb.entity.SignalProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
@Data
@Accessors(chain = true)
public class SignalDTO {
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer signalId;
    private Boolean enable;
    private Boolean visible;
    private String description;
    private String signalName;
    private Integer signalCategory;
    private Integer signalType;
    private Integer channelNo;
    private Integer channelType;
    private String expression;
    private Integer dataType;
    private String showPrecision;
    private String unit;
    private Double storeInterval;
    private Double absValueThreshold;
    private Double percentThreshold;
    private Integer staticsPeriod;
    private Long baseTypeId;
    private String baseTypeName;
    private Integer baseStatusId;
    private String baseNameExt;
    private Double chargeStoreInterVal;
    private Double chargeAbsValue;
    private Integer displayIndex;
    private Integer mDBSignalId;
    private Integer moduleNo;
    private List<SignalProperty> signalPropertyList;
    private List<SignalMeanings> signalMeaningsList;

    private LifeCycleEventType eventType;

    public ForeignSignal toForeignSignal(String foreignGatewayId,Integer monitorUnitId, String foreignDeviceId,Integer equipmentId,String foreignSignalId) {
        ForeignSignal foreignSignal = new ForeignSignal();
        foreignSignal.setForeignSignalID(foreignSignalId)
                .setForeignDeviceID(foreignDeviceId)
                .setMonitorUnitId(monitorUnitId)
                .setForeignGatewayID(foreignGatewayId)
                .setEquipmentId(equipmentId)
                .setSignalId(signalId);
        return foreignSignal;
    }
}
