package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.bean.BeanUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignGateway;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import lombok.Data;

/**
 * 外部网关数据传输类
 */
@Data
public class ForeignGatewayDTO extends ForeignGateway {
    private LifeCycleEventType lifeCycleEventType;

    public static ForeignGatewayDTO fromForeignGateway(ForeignGateway foreignGateway){
        ForeignGatewayDTO foreignGatewayDTO = new ForeignGatewayDTO();
        BeanUtil.copyProperties(foreignGateway,foreignGatewayDTO);
        return foreignGatewayDTO;
    }

    public void copyField(ForeignGatewayDTO oldForeignGatewayDTO){
        oldForeignGatewayDTO.setStationId(getStationId());
        oldForeignGatewayDTO.setForeignGatewayID(getForeignGatewayID());
        oldForeignGatewayDTO.setPluginId(getPluginId());
        oldForeignGatewayDTO.setMonitorUnitID(getMonitorUnitID());
        oldForeignGatewayDTO.setLifeCycleEventType(lifeCycleEventType);
    }
}
