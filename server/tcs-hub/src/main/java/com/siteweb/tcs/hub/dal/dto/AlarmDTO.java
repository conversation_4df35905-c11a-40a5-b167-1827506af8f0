package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignAlarm;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AlarmDTO {
    private Integer id;
    /**
     * 设备模板id
     */
    private Integer equipmentTemplateId;
    /**
     * 事件id
     */
    private Integer eventId;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * 开始类型
     */
    private Integer startType;
    /**
     * 结束类型
     */
    private Integer endType;
    /**
     * 开始表达式
     */
    private String startExpression;
    /**
     * 抑制表达式
     */
    private String suppressExpression;
    /**
     * 事件类别
     */
    private Integer eventCategory;
    /**
     * 信号主键id
     */
    private Integer signalId;
    /**
     * 是否启用
     */
    private Boolean enable;
    /**
     * 是否可见
     */
    private Boolean visible;
    /**
     * 描述
     */
    private String description;
    /**
     * 显示顺序
     */
    private Integer displayIndex;
    /**
     * 所属模块
     */
    private Integer moduleNo;

    private EventConditionDTO eventConditionDTO;

    private LifeCycleEventType eventType;

    /**
     * e.getForeignGatewayID(),handleDeviceLifeCycleRequest.getMonitorUnitId(),e.getForeignDeviceID(),a.getForeignAlarmId())
     * @param foreignDeviceId
     * @param foreignAlarmId
     * @param equipmentId
     * @return
     */
    public ForeignAlarm toForeignAlarm(String foreignGatewayID,Integer monitorUnitId,String  foreignDeviceId,Integer equipmentId,String foreignAlarmId) {
        ForeignAlarm foreignAlarm = new ForeignAlarm()
                .setForeignDeviceID(foreignDeviceId)
                .setForeignAlarmID(foreignAlarmId)
                .setEquipmentId(equipmentId)
                .setEventId(eventId)
                .setMonitorUnitId(monitorUnitId)
                .setForeignGatewayID(foreignGatewayID);
        if(ObjectUtil.isNotEmpty(eventConditionDTO)) foreignAlarm.setEventConditionId(eventConditionDTO.getEventConditionId());
        return foreignAlarm;
    }
}
