package com.siteweb.tcs.hub.dal.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("tcs_account")
public class Account {

    @TableId(value = "userId", type = IdType.AUTO)
    private Integer userId;

    private String userName;

    private String loginId;

    private String password;

    private Boolean enable;

    private Integer maxError;

    private Boolean locked;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date validTime;

    private Date passwordValidTime;

    private String description;
}
