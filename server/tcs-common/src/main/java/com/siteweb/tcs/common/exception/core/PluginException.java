package com.siteweb.tcs.common.exception.core;

import com.siteweb.tcs.common.exception.code.ErrorCode;
import com.siteweb.tcs.common.exception.code.PluginErrorCode;
import lombok.Getter;

import java.io.Serial;

/**
 * Plugin exception class for plugin-related exceptions.
 * This exception type should be used for all plugin-related errors.
 */
@Getter
public class PluginException extends TCSException {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Plugin ID that generated the exception
     */
    private final String pluginId;

    /**
     * Plugin name that generated the exception
     */
    private final String pluginName;

    public PluginException(String code, String message, String pluginId) {
        this(code, message, null, null, pluginId, null);
    }

    public PluginException(String code, String message, String pluginId, String pluginName) {
        this(code, message, null, null, pluginId, pluginName);
    }

    public PluginException(String code, String message, Object details, String pluginId) {
        this(code, message, details, null, pluginId, null);
    }

    public PluginException(String code, String message, Throwable cause, String pluginId) {
        this(code, message, null, cause, pluginId, null);
    }

    public PluginException(String code, String message, Object details, Throwable cause, String pluginId) {
        this(code, message, details, cause, pluginId, null);
    }

    public PluginException(String code, String message, Object details, Throwable cause, String pluginId, String pluginName) {
        super(code, message, details, cause);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginException(PluginErrorCode errorCode, String pluginId) {
        super(errorCode);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginException(PluginErrorCode errorCode, String pluginId, String pluginName) {
        super(errorCode);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginException(PluginErrorCode errorCode, Object details, String pluginId, String pluginName) {
        super(errorCode, details);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginException(PluginErrorCode errorCode, Throwable cause, String pluginId, String pluginName) {
        super(errorCode, cause);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }

    public PluginException(PluginErrorCode errorCode, Object details, Throwable cause, String pluginId) {
        super(errorCode, details, cause);
        this.pluginId = pluginId;
        this.pluginName = null;
    }

    public PluginException(PluginErrorCode errorCode, Object details, Throwable cause, String pluginId, String pluginName) {
        super(errorCode, details, cause);
        this.pluginId = pluginId;
        this.pluginName = pluginName;
    }
}
