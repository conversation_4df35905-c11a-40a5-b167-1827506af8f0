package com.siteweb.tcs.common.runtime;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR> (2024-06-25)
 **/
public class SSeEmitterCollection {
    private final CopyOnWriteArrayList<SseEmitter> emitters = new CopyOnWriteArrayList<>();


    public void add(SseEmitter emitter) {
        emitters.add(emitter);
    }


    public void remove(SseEmitter emitter) {
        emitters.remove(emitter);
    }


    public void append(PluginLogCollector.LogInfo logMessage) {
        for (SseEmitter emitter : emitters) {
            try {
                emitter.send(SseEmitter.event().name("append").data(logMessage));
            } catch (IOException e) {
                emitters.remove(emitter);
            }
        }
    }

    public boolean connected(SseEmitter emitter, Object logMessage) {
        try {
            emitter.send(SseEmitter.event().name("connected").data(logMessage));
            return true;
        } catch (IOException e) {
            emitters.remove(emitter);
            return false;
        }
    }


    public void clean() {
        for (SseEmitter emitter : emitters) {
            try {
                emitter.send(SseEmitter.event().name("clean").data(""));
            } catch (IOException e) {
                emitters.remove(emitter);
            }
        }
    }


}
