package com.siteweb.tcs.common.response;

import com.siteweb.tcs.common.exception.code.BusinessErrorCode;
import com.siteweb.tcs.common.exception.core.BusinessException;
import com.siteweb.tcs.common.exception.core.PluginException;
import com.siteweb.tcs.common.exception.core.TCSException;
import com.siteweb.tcs.common.exception.core.TechnicalException;
import com.siteweb.tcs.common.exception.plugin.PluginBusinessException;
import com.siteweb.tcs.common.exception.plugin.PluginTechnicalException;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * API返回结果打包工具类
 */
public abstract class ResponseHelper {
    /**
     * 构建成功返回值，结构体为空
     *
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> successful() {
        return successful((Object) null);
    }

    /**
     * 根据操作结果，构建正常API返回结果
     *
     * @param data 业务操作结果，如查询结果等
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> successful(Object data) {
        ResponseResult result = new ResponseResult();
        result.setState(true);
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        result.setErrCode(null);
        return new ResponseEntity<ResponseResult>(result, HttpStatus.OK);
    }

    /**
     * 根据错误码，错误消息，http状态码都见API返回结果
     *
     * @param code    错误码
     * @param message 错误消息
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(String code, String message) {
        return failed(code, message, null, HttpStatus.OK);
    }


    /**
     *
     * @param businessErrorCode
     * @param details
     * @return
     */
    public static ResponseEntity<ResponseResult> failed(BusinessErrorCode businessErrorCode, String details) {
        return failed(businessErrorCode.getCode(), businessErrorCode.getMessage(), details, HttpStatus.OK);
    }



    /**
     * 根据错误码，错误消息，错误详情构建API返回结果
     *
     * @param code    错误码
     * @param message 错误消息
     * @param details 错误详情
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(String code, String message, Object details) {
        return failed(code, message, details, HttpStatus.BAD_REQUEST);
    }

    /**
     * 根据错误码，错误消息，HTTP状态码构建API返回结果
     *
     * @param code       错误码
     * @param message    错误消息
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(String code, String message, HttpStatus httpStatus) {
        return failed(code, message, null, httpStatus);
    }

    /**
     * 根据错误码，错误消息，错误详情，HTTP状态码构建API返回结果
     *
     * @param code       错误码
     * @param message    错误消息
     * @param details    错误详情
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(String code, String message, Object details, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(code);
        result.setErrMsg(message);
        result.setTimestamp(System.currentTimeMillis());
        result.setData(details);
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据整数错误码，错误消息构建API返回结果
     *
     * @param code    错误码
     * @param message 错误消息
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(int code, String message) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(String.valueOf(code));
        result.setErrMsg(message);
        result.setTimestamp(System.currentTimeMillis());
        return new ResponseEntity<ResponseResult>(result, HttpStatus.BAD_REQUEST);
    }

    /**
     * 根据错误消息返回API结果，HTTP状态码为500
     *
     * @param message 错误消息
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(String message) {
        return failed("-1", message);
    }

    /**
     * 根据TCS异常返回API结果
     *
     * @param ex TCS异常
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(TCSException ex) {
        return failed(ex, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 根据TCS异常及HTTP状态码构建返回API结果
     *
     * @param ex         TCS异常
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(TCSException ex, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        result.setData(ex.getDetails());
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据业务异常返回API结果
     *
     * @param ex 业务异常
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(BusinessException ex) {
        return failed(ex, HttpStatus.BAD_REQUEST);
    }

    /**
     * 根据业务异常及HTTP状态码构建返回API结果
     *
     * @param ex         业务异常
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(BusinessException ex, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        result.setData(ex.getDetails());
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据技术异常返回API结果
     *
     * @param ex 技术异常
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(TechnicalException ex) {
        return failed(ex, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 根据技术异常及HTTP状态码构建返回API结果
     *
     * @param ex         技术异常
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(TechnicalException ex, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        if (ex.getComponent() != null) {
            details.put("component", ex.getComponent());
        }
        
        result.setData(details.isEmpty() ? null : details);
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据插件异常返回API结果
     *
     * @param ex 插件异常
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(PluginException ex) {
        return failed(ex, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 根据插件异常及HTTP状态码构建返回API结果
     *
     * @param ex         插件异常
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(PluginException ex, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        details.put("pluginId", ex.getPluginId());
        if (ex.getPluginName() != null) {
            details.put("pluginName", ex.getPluginName());
        }
        
        result.setData(details);
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据插件业务异常返回API结果
     *
     * @param ex 插件业务异常
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(PluginBusinessException ex) {
        return failed(ex, HttpStatus.BAD_REQUEST);
    }

    /**
     * 根据插件业务异常及HTTP状态码构建返回API结果
     *
     * @param ex         插件业务异常
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(PluginBusinessException ex, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        details.put("pluginId", ex.getPluginId());
        if (ex.getPluginName() != null) {
            details.put("pluginName", ex.getPluginName());
        }
        
        result.setData(details);
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据插件技术异常返回API结果
     *
     * @param ex 插件技术异常
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(PluginTechnicalException ex) {
        return failed(ex, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * 根据插件技术异常及HTTP状态码构建返回API结果
     *
     * @param ex         插件技术异常
     * @param httpStatus HTTP状态码
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> failed(PluginTechnicalException ex, HttpStatus httpStatus) {
        ResponseResult result = new ResponseResult();
        result.setState(false);
        result.setErrCode(ex.getCode());
        result.setErrMsg(ex.getMessage());
        result.setTimestamp(System.currentTimeMillis());
        
        Map<String, Object> details = new HashMap<>();
        if (ex.getDetails() != null) {
            details.put("details", ex.getDetails());
        }
        details.put("pluginId", ex.getPluginId());
        if (ex.getPluginName() != null) {
            details.put("pluginName", ex.getPluginName());
        }
        if (ex.getComponent() != null) {
            details.put("component", ex.getComponent());
        }
        
        result.setData(details);
        return new ResponseEntity<ResponseResult>(result, httpStatus);
    }

    /**
     * 根据操作结果，构建正常API返回结果，分页使用
     *
     * @param data  业务操作结果，如查询结果等
     * @param total 数据总数
     * @param pages 总页数
     * @return API返回结果
     */
    public static ResponseEntity<ResponseResult> successful(Object data, long total, long pages) {
        PageResponseResult result = new PageResponseResult();
        result.setState(true);
        result.setData(data);
        result.setTimestamp(System.currentTimeMillis());
        result.setTotal(total);
        result.setPages(pages);
        return new ResponseEntity<ResponseResult>(result, HttpStatus.OK);
    }

    /**
     * 文件下载
     *
     * @param file 文件
     * @return 文件下载响应
     */
    public static ResponseEntity<FileSystemResource> download(File file) {
        FileSystemResource resource = new FileSystemResource(file);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Content-Disposition", "attachment; filename=" + file.getName());
        return ResponseEntity.ok()
                .headers(headers)
                .contentLength(file.length())
                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(resource);
    }
}
