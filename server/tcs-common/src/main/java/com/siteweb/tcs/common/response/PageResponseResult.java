package com.siteweb.tcs.common.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @author: Habits
 * @time: 2022/3/17 10:32
 * @description: 分页对象
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageResponseResult extends ResponseResult {

    /**
     * 总条数
     */
    private long total;

    /**
     * 总页数
     */
    private long pages;
}
