package com.siteweb.tcs.common.runtime;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import org.apache.pekko.http.javadsl.marshalling.Marshaller;
import org.apache.pekko.http.javadsl.model.HttpEntity;
import org.apache.pekko.http.javadsl.model.MediaType;
import org.apache.pekko.http.javadsl.model.MediaTypes;
import org.apache.pekko.http.javadsl.model.RequestEntity;
import org.apache.pekko.http.javadsl.unmarshalling.Unmarshaller;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

public class JacksonXmlSupport {
    private static final ObjectMapper DEFAULT_XML_MAPPER =
            new XmlMapper().enable(SerializationFeature.WRAP_ROOT_VALUE);
    private static final List<MediaType> XML_MEDIA_TYPES = Arrays.asList(MediaTypes.APPLICATION_XML, MediaTypes.TEXT_XML);

    public static <T> Marshaller<T, RequestEntity> marshaller() {
        return Marshaller.wrapEntity(
                u -> toXML(DEFAULT_XML_MAPPER, u),
                Marshaller.stringToEntity(),
                MediaTypes.APPLICATION_XML
        );
    }

    public static <T> Unmarshaller<HttpEntity, T> unmarshaller(Class<T> expectedType) {
        return Unmarshaller.forMediaTypes(XML_MEDIA_TYPES, Unmarshaller.entityToString())
                .thenApply(xml -> fromXML(DEFAULT_XML_MAPPER, xml, expectedType));
    }

    public static <T> String toXML(ObjectMapper mapper, T object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (IOException e) {
            throw new IllegalArgumentException("Cannot marshal to XML: " + object, e);
        }
    }

    private static <T> T fromXML(ObjectMapper mapper, String xml, Class<T> expectedType) {
        try {
            return mapper.readerFor(expectedType).readValue(xml);
        } catch (IOException e) {
            throw new IllegalArgumentException("Cannot unmarshal XML as " + expectedType.getSimpleName(), e);
        }
    }
}
