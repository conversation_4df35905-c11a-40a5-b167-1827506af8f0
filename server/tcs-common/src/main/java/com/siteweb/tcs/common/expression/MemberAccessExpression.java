package com.siteweb.tcs.common.expression;

import com.siteweb.tcs.common.expression.enums.MemberAccessScope;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 成员访问表达式
 * <AUTHOR> (2025-02-28)
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MemberAccessExpression {
    /**
     * 属性范围、性质
     */
    private MemberAccessScope scope;

    /**
     * 属性名
     */
    private String property;

    public String toExpression() {
        return scope.buildMethod.apply(property);
    }


}
