package com.siteweb.tcs.common.o11y;

import org.apache.pekko.actor.ActorSystem;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * 测试配置类
 * 用于在测试环境中初始化必要的组件
 */
@SpringBootConfiguration
public class TestConfig {

    /**
     * 创建测试用的ActorSystem
     */
    @Bean
    @Primary
    public ActorSystem actorSystem() {
        return ActorSystem.create("test-system");
    }

    /**
     * 创建ProbeManagerImpl实例
     */
//    @Bean
//    @Primary
//    public ProbeManagerImpl probeManagerImpl(ActorSystem actorSystem) {
//        ProbeManagerImpl probeManagerImpl = new ProbeManagerImpl(actorSystem);
//
//        // 将ProbeManagerImpl实例注入到ProbeManagerAdapter中
//        ProbeManagerAdapter.setProbeManagerImpl(probeManagerImpl);
//
//        return probeManagerImpl;
//    }
}