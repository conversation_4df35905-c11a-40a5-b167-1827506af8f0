# ==================================================
# Switch Shape
# ==================================================
streams.shapes.data-switch.name=数据条件分支
streams.shapes.data-switch.alias=条件分支
streams.shapes.data-switch.tooltip=通过条件控制数据流走向
streams.shapes.data-switch.groups=基础
streams.shapes.data-switch.tags=Switch,分支,判断,条件
streams.shapes.data-switch.inlet1.name=In
streams.shapes.data-switch.outlet1.name=Out-{index}
