package com.siteweb.stream.south.seed.shapes;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.BytesMessage;
import com.siteweb.stream.common.messages.TextMessage;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.south.seed.options.SeedDataMapperOption;
import com.siteweb.stream.south.seed.options.defaults.SeedDataMapperDefaultOption;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR> (2025-04-25)
 **/

@Slf4j
@EditorHidden
@Shape(type = "seed-data-mapper")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeDefaultOptions(SeedDataMapperDefaultOption.class)
@ShapeInlet(id = 0x01, type = TextMessage.class,desc = "响应结果")
@ShapeOutlet(id = 0x02, type = StreamMessage.class, desc = "映射结果")
public class SeedDataMapper extends AbstractShape {

    @Recoverable
    private SeedDataMapperOption options;


    public SeedDataMapper(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof SeedDataMapperOption timerShapeOptions) {
            this.options = timerShapeOptions;
            // 配置重置后 恢复计时器状态
            if (isStarted()) onStart();
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {


        // do something


        // send data message to Outlet
        var message = new BytesMessage();
        message.setPayload();
        context.getOutLet((short) 0x02).broadcast(message);
    }

}
